# Redis Vector Embeddings Configuration

# Environment
ENVIRONMENT=development
DEBUG=false

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=
REDIS_DECODE_RESPONSES=false
REDIS_SOCKET_TIMEOUT=30
REDIS_SOCKET_CONNECT_TIMEOUT=30
REDIS_MAX_CONNECTIONS=50

# Embedding Service Configuration
# Options: openai, ollama, sentence_transformers
EMBEDDING_PROVIDER=sentence_transformers
EMBEDDING_MODEL=all-MiniLM-L6-v2
EMBEDDING_DIMENSIONS=
EMBEDDING_MAX_RETRIES=3
EMBEDDING_TIMEOUT=30
EMBEDDING_BATCH_SIZE=32

# OpenAI Configuration (if using OpenAI embeddings)
OPENAI_API_KEY=your_openai_api_key_here
# Alternative models: text-embedding-3-small, text-embedding-3-large, text-embedding-ada-002

# Ollama Configuration (if using Ollama embeddings)
OLLAMA_BASE_URL=http://localhost:11434
# Popular embedding models: nomic-embed-text, mxbai-embed-large

# Vector Store Configuration
VECTOR_INDEX_NAME=vector_qa
VECTOR_DOC_PREFIX=doc:
VECTOR_SIMILARITY_THRESHOLD=0.7
VECTOR_MAX_RESULTS=10
VECTOR_ENABLE_METADATA_FILTERING=true
VECTOR_ENABLE_CACHING=true
VECTOR_CACHE_TTL=3600

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_RELOAD=false
API_WORKERS=1
API_LOG_LEVEL=info

# CORS Configuration
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,*
CORS_CREDENTIALS=false
CORS_METHODS=*
CORS_HEADERS=*

# LLM Configuration
LLM_PROVIDER=ollama
LLM_MODEL=qwen3:8b-q4_K_M
LLM_BASE_URL=http://localhost:11434
LLM_API_KEY=
LLM_TEMPERATURE=0.5
LLM_MAX_TOKENS=150
LLM_TIMEOUT=120

# Additional API Keys (if needed)
EMBEDDING_API_KEY=
LLM_API_KEY=
