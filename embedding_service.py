"""
Enhanced embedding service supporting multiple providers:
- OpenAI API
- Ollama local models
- Sentence Transformers (local)
"""

import os
import numpy as np
import requests
import json
from typing import List, Dict, Any, Optional, Union
from enum import Enum
from dataclasses import dataclass
from sentence_transformers import SentenceTransformer
import openai
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class EmbeddingProvider(Enum):
    OPENAI = "openai"
    OLLAMA = "ollama"
    SENTENCE_TRANSFORMERS = "sentence_transformers"

@dataclass
class EmbeddingConfig:
    provider: EmbeddingProvider
    model_name: str
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    dimensions: Optional[int] = None
    max_retries: int = 3
    timeout: int = 30

class EmbeddingService:
    """Unified embedding service supporting multiple providers"""
    
    def __init__(self, config: EmbeddingConfig):
        self.config = config
        self.provider = config.provider
        self._client = None
        self._model = None
        self._initialize_provider()
    
    def _initialize_provider(self):
        """Initialize the embedding provider"""
        try:
            if self.provider == EmbeddingProvider.OPENAI:
                self._initialize_openai()
            elif self.provider == EmbeddingProvider.OLLAMA:
                self._initialize_ollama()
            elif self.provider == EmbeddingProvider.SENTENCE_TRANSFORMERS:
                self._initialize_sentence_transformers()
            else:
                raise ValueError(f"Unsupported provider: {self.provider}")
            
            print(f"✅ Initialized {self.provider.value} embedding provider")
        except Exception as e:
            print(f"❌ Failed to initialize {self.provider.value}: {e}")
            raise
    
    def _initialize_openai(self):
        """Initialize OpenAI client"""
        api_key = self.config.api_key or os.getenv("OPENAI_API_KEY")
        if not api_key:
            raise ValueError("OpenAI API key not provided")
        
        self._client = openai.OpenAI(api_key=api_key)
        
        # Test the connection
        try:
            response = self._client.embeddings.create(
                model=self.config.model_name,
                input="test",
                encoding_format="float"
            )
            self.config.dimensions = len(response.data[0].embedding)
        except Exception as e:
            print(f"⚠️ OpenAI connection test failed: {e}")
    
    def _initialize_ollama(self):
        """Initialize Ollama client"""
        base_url = self.config.base_url or "http://localhost:11434"
        self.config.base_url = base_url
        
        # Test connection
        try:
            response = requests.get(f"{base_url}/api/tags", timeout=5)
            if response.status_code != 200:
                raise ConnectionError(f"Ollama server not accessible: {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"⚠️ Ollama connection test failed: {e}")
    
    def _initialize_sentence_transformers(self):
        """Initialize Sentence Transformers model"""
        try:
            self._model = SentenceTransformer(self.config.model_name)
            # Get embedding dimensions
            test_embedding = self._model.encode("test")
            self.config.dimensions = len(test_embedding)
        except Exception as e:
            print(f"⚠️ Sentence Transformers initialization failed: {e}")
            raise
    
    def get_embedding(self, text: str) -> np.ndarray:
        """Get embedding for a single text"""
        embeddings = self.get_embeddings([text])
        return embeddings[0] if embeddings else np.array([])
    
    def get_embeddings(self, texts: List[str]) -> List[np.ndarray]:
        """Get embeddings for multiple texts"""
        if not texts:
            return []
        
        try:
            if self.provider == EmbeddingProvider.OPENAI:
                return self._get_openai_embeddings(texts)
            elif self.provider == EmbeddingProvider.OLLAMA:
                return self._get_ollama_embeddings(texts)
            elif self.provider == EmbeddingProvider.SENTENCE_TRANSFORMERS:
                return self._get_sentence_transformer_embeddings(texts)
            else:
                raise ValueError(f"Unsupported provider: {self.provider}")
        except Exception as e:
            print(f"❌ Error getting embeddings: {e}")
            return []
    
    def _get_openai_embeddings(self, texts: List[str]) -> List[np.ndarray]:
        """Get embeddings from OpenAI API"""
        try:
            response = self._client.embeddings.create(
                model=self.config.model_name,
                input=texts,
                encoding_format="float"
            )
            
            embeddings = []
            for data in response.data:
                embeddings.append(np.array(data.embedding, dtype=np.float32))
            
            return embeddings
        except Exception as e:
            print(f"❌ OpenAI embedding error: {e}")
            return []
    
    def _get_ollama_embeddings(self, texts: List[str]) -> List[np.ndarray]:
        """Get embeddings from Ollama"""
        embeddings = []
        
        for text in texts:
            try:
                url = f"{self.config.base_url}/api/embeddings"
                payload = {
                    "model": self.config.model_name,
                    "prompt": text
                }
                
                response = requests.post(
                    url, 
                    json=payload, 
                    timeout=self.config.timeout
                )
                
                if response.status_code == 200:
                    result = response.json()
                    embedding = np.array(result.get("embedding", []), dtype=np.float32)
                    embeddings.append(embedding)
                else:
                    print(f"❌ Ollama embedding error: {response.status_code}")
                    embeddings.append(np.array([]))
                    
            except Exception as e:
                print(f"❌ Ollama embedding error for text: {e}")
                embeddings.append(np.array([]))
        
        return embeddings
    
    def _get_sentence_transformer_embeddings(self, texts: List[str]) -> List[np.ndarray]:
        """Get embeddings from Sentence Transformers"""
        try:
            embeddings = self._model.encode(texts, convert_to_numpy=True)
            return [emb.astype(np.float32) for emb in embeddings]
        except Exception as e:
            print(f"❌ Sentence Transformers embedding error: {e}")
            return []
    
    def get_dimensions(self) -> int:
        """Get embedding dimensions"""
        return self.config.dimensions or 0
    
    def get_provider_info(self) -> Dict[str, Any]:
        """Get provider information"""
        return {
            "provider": self.provider.value,
            "model_name": self.config.model_name,
            "dimensions": self.config.dimensions,
            "base_url": self.config.base_url
        }

class EmbeddingServiceFactory:
    """Factory for creating embedding services with different configurations"""
    
    @staticmethod
    def create_openai_service(
        model_name: str = "text-embedding-3-small",
        api_key: Optional[str] = None
    ) -> EmbeddingService:
        """Create OpenAI embedding service"""
        config = EmbeddingConfig(
            provider=EmbeddingProvider.OPENAI,
            model_name=model_name,
            api_key=api_key
        )
        return EmbeddingService(config)
    
    @staticmethod
    def create_ollama_service(
        model_name: str = "nomic-embed-text",
        base_url: str = "http://localhost:11434"
    ) -> EmbeddingService:
        """Create Ollama embedding service"""
        config = EmbeddingConfig(
            provider=EmbeddingProvider.OLLAMA,
            model_name=model_name,
            base_url=base_url
        )
        return EmbeddingService(config)
    
    @staticmethod
    def create_sentence_transformer_service(
        model_name: str = "all-MiniLM-L6-v2"
    ) -> EmbeddingService:
        """Create Sentence Transformers embedding service"""
        config = EmbeddingConfig(
            provider=EmbeddingProvider.SENTENCE_TRANSFORMERS,
            model_name=model_name
        )
        return EmbeddingService(config)
    
    @staticmethod
    def create_from_env() -> EmbeddingService:
        """Create embedding service from environment variables"""
        provider_name = os.getenv("EMBEDDING_PROVIDER", "sentence_transformers").lower()
        model_name = os.getenv("EMBEDDING_MODEL", "all-MiniLM-L6-v2")
        
        if provider_name == "openai":
            return EmbeddingServiceFactory.create_openai_service(
                model_name=model_name,
                api_key=os.getenv("OPENAI_API_KEY")
            )
        elif provider_name == "ollama":
            return EmbeddingServiceFactory.create_ollama_service(
                model_name=model_name,
                base_url=os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
            )
        else:
            return EmbeddingServiceFactory.create_sentence_transformer_service(
                model_name=model_name
            )

# Global embedding service instance
_embedding_service: Optional[EmbeddingService] = None

def get_embedding_service() -> EmbeddingService:
    """Get the global embedding service instance"""
    global _embedding_service
    if _embedding_service is None:
        _embedding_service = EmbeddingServiceFactory.create_from_env()
    return _embedding_service

def set_embedding_service(service: EmbeddingService):
    """Set the global embedding service instance"""
    global _embedding_service
    _embedding_service = service
