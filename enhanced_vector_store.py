"""
Enhanced Redis Vector Store with improved search capabilities and metadata support
"""

import json
import hashlib
import numpy as np
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple, Union
from dataclasses import dataclass, asdict
from redis import Redis
from sklearn.metrics.pairwise import cosine_similarity
from embedding_service import get_embedding_service, EmbeddingService

@dataclass
class VectorDocument:
    """Document with vector embedding and metadata"""
    id: str
    text: str
    embedding: List[float]
    metadata: Dict[str, Any]
    timestamp: int
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'VectorDocument':
        return cls(**data)

@dataclass
class SearchResult:
    """Search result with similarity score and metadata"""
    document: VectorDocument
    similarity_score: float
    rank: int
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.document.id,
            "text": self.document.text,
            "metadata": self.document.metadata,
            "similarity_score": self.similarity_score,
            "rank": self.rank,
            "timestamp": self.document.timestamp
        }

@dataclass
class SearchConfig:
    """Configuration for vector search"""
    similarity_threshold: float = 0.7
    max_results: int = 10
    include_metadata: bool = True
    rerank_results: bool = True
    filter_metadata: Optional[Dict[str, Any]] = None

class EnhancedVectorStore:
    """Enhanced Redis vector store with multiple embedding providers and advanced search"""
    
    def __init__(
        self, 
        redis_client: Optional[Redis] = None,
        embedding_service: Optional[EmbeddingService] = None,
        index_name: str = "vector_qa",
        doc_prefix: str = "doc:"
    ):
        self.redis = redis_client or Redis(host='localhost', port=6379, db=0, decode_responses=False)
        self.embedding_service = embedding_service or get_embedding_service()
        self.index_name = index_name
        self.doc_prefix = doc_prefix
        self.metadata_prefix = f"{doc_prefix}meta:"
        
        # Test Redis connection
        try:
            self.redis.ping()
            print("✅ Redis connection successful")
        except Exception as e:
            print(f"❌ Redis connection failed: {e}")
            raise
    
    def _generate_doc_id(self, text: str, metadata: Optional[Dict[str, Any]] = None) -> str:
        """Generate a unique document ID"""
        content = text
        if metadata:
            content += json.dumps(metadata, sort_keys=True)
        return hashlib.sha256(content.encode('utf-8')).hexdigest()[:16]
    
    def store_document(
        self, 
        text: str, 
        metadata: Optional[Dict[str, Any]] = None,
        doc_id: Optional[str] = None
    ) -> str:
        """Store a document with its vector embedding"""
        try:
            # Generate embedding
            embedding = self.embedding_service.get_embedding(text)
            if embedding.size == 0:
                raise ValueError("Failed to generate embedding")
            
            # Generate document ID if not provided
            if doc_id is None:
                doc_id = self._generate_doc_id(text, metadata)
            
            # Create document
            document = VectorDocument(
                id=doc_id,
                text=text,
                embedding=embedding.tolist(),
                metadata=metadata or {},
                timestamp=int(datetime.utcnow().timestamp() * 1000)
            )
            
            # Store in Redis
            doc_key = f"{self.doc_prefix}{doc_id}"
            self.redis.set(doc_key, json.dumps(document.to_dict()).encode('utf-8'))
            
            # Store metadata separately for efficient filtering
            if metadata:
                meta_key = f"{self.metadata_prefix}{doc_id}"
                self.redis.set(meta_key, json.dumps(metadata).encode('utf-8'))
            
            print(f"✅ Stored document {doc_id}: {text[:50]}...")
            return doc_id
            
        except Exception as e:
            print(f"❌ Error storing document: {e}")
            raise
    
    def store_qa_pair(
        self, 
        question: str, 
        answer: str, 
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """Store a question-answer pair"""
        qa_metadata = {
            "type": "qa_pair",
            "question": question,
            "answer": answer,
            **(metadata or {})
        }
        
        # Use question for embedding and search
        return self.store_document(question, qa_metadata)
    
    def get_document(self, doc_id: str) -> Optional[VectorDocument]:
        """Retrieve a document by ID"""
        try:
            doc_key = f"{self.doc_prefix}{doc_id}"
            doc_data = self.redis.get(doc_key)
            
            if doc_data:
                data = json.loads(doc_data.decode('utf-8'))
                return VectorDocument.from_dict(data)
            return None
            
        except Exception as e:
            print(f"❌ Error retrieving document {doc_id}: {e}")
            return None
    
    def search_similar(
        self, 
        query: str, 
        config: Optional[SearchConfig] = None
    ) -> List[SearchResult]:
        """Search for similar documents"""
        if config is None:
            config = SearchConfig()
        
        try:
            # Generate query embedding
            query_embedding = self.embedding_service.get_embedding(query)
            if query_embedding.size == 0:
                print("❌ Failed to generate query embedding")
                return []
            
            # Get all document keys
            doc_keys = self.redis.keys(f"{self.doc_prefix}*")
            if not doc_keys:
                return []
            
            # Filter out metadata keys
            doc_keys = [key for key in doc_keys if not key.decode('utf-8').startswith(self.metadata_prefix)]
            
            results = []
            for key in doc_keys:
                try:
                    doc_data = self.redis.get(key)
                    if not doc_data:
                        continue
                    
                    data = json.loads(doc_data.decode('utf-8'))
                    document = VectorDocument.from_dict(data)
                    
                    # Apply metadata filters
                    if config.filter_metadata and not self._matches_filter(document.metadata, config.filter_metadata):
                        continue
                    
                    # Calculate similarity
                    doc_embedding = np.array(document.embedding, dtype=np.float32)
                    similarity = self._calculate_similarity(query_embedding, doc_embedding)
                    
                    # Apply similarity threshold
                    if similarity >= config.similarity_threshold:
                        results.append((similarity, document))
                        
                except Exception as e:
                    print(f"⚠️ Error processing document {key}: {e}")
                    continue
            
            # Sort by similarity
            results.sort(key=lambda x: x[0], reverse=True)
            
            # Limit results
            results = results[:config.max_results]
            
            # Create SearchResult objects
            search_results = []
            for rank, (similarity, document) in enumerate(results, 1):
                search_results.append(SearchResult(
                    document=document,
                    similarity_score=similarity,
                    rank=rank
                ))
            
            return search_results
            
        except Exception as e:
            print(f"❌ Error in similarity search: {e}")
            return []
    
    def search_qa_pairs(
        self, 
        question: str, 
        config: Optional[SearchConfig] = None
    ) -> List[Dict[str, Any]]:
        """Search for similar question-answer pairs"""
        if config is None:
            config = SearchConfig()
        
        # Filter for QA pairs only
        qa_filter = {"type": "qa_pair"}
        if config.filter_metadata:
            qa_filter.update(config.filter_metadata)
        
        config.filter_metadata = qa_filter
        
        results = self.search_similar(question, config)
        
        # Format results for QA pairs
        qa_results = []
        for result in results:
            metadata = result.document.metadata
            qa_results.append({
                "id": result.document.id,
                "question": metadata.get("question", result.document.text),
                "answer": metadata.get("answer", ""),
                "similarity_score": result.similarity_score,
                "rank": result.rank,
                "timestamp": result.document.timestamp,
                "metadata": {k: v for k, v in metadata.items() if k not in ["question", "answer", "type"]}
            })
        
        return qa_results
    
    def _calculate_similarity(self, vec1: np.ndarray, vec2: np.ndarray) -> float:
        """Calculate cosine similarity between two vectors"""
        try:
            # Ensure vectors are 2D for sklearn
            vec1 = vec1.reshape(1, -1)
            vec2 = vec2.reshape(1, -1)
            
            similarity = cosine_similarity(vec1, vec2)[0][0]
            return float(similarity)
        except Exception as e:
            print(f"⚠️ Error calculating similarity: {e}")
            return 0.0
    
    def _matches_filter(self, metadata: Dict[str, Any], filter_dict: Dict[str, Any]) -> bool:
        """Check if metadata matches filter criteria"""
        for key, value in filter_dict.items():
            if key not in metadata or metadata[key] != value:
                return False
        return True
    
    def get_stats(self) -> Dict[str, Any]:
        """Get vector store statistics"""
        try:
            doc_keys = self.redis.keys(f"{self.doc_prefix}*")
            doc_keys = [key for key in doc_keys if not key.decode('utf-8').startswith(self.metadata_prefix)]
            
            total_docs = len(doc_keys)
            
            # Count QA pairs
            qa_count = 0
            for key in doc_keys[:100]:  # Sample first 100 for performance
                try:
                    doc_data = self.redis.get(key)
                    if doc_data:
                        data = json.loads(doc_data.decode('utf-8'))
                        if data.get('metadata', {}).get('type') == 'qa_pair':
                            qa_count += 1
                except:
                    continue
            
            return {
                "total_documents": total_docs,
                "qa_pairs": qa_count,
                "embedding_provider": self.embedding_service.get_provider_info(),
                "index_name": self.index_name,
                "redis_connected": True
            }
            
        except Exception as e:
            return {
                "error": str(e),
                "redis_connected": False
            }
    
    def delete_document(self, doc_id: str) -> bool:
        """Delete a document"""
        try:
            doc_key = f"{self.doc_prefix}{doc_id}"
            meta_key = f"{self.metadata_prefix}{doc_id}"
            
            # Delete both document and metadata
            deleted = self.redis.delete(doc_key, meta_key)
            return deleted > 0
            
        except Exception as e:
            print(f"❌ Error deleting document {doc_id}: {e}")
            return False
    
    def clear_all(self) -> int:
        """Clear all documents from the vector store"""
        try:
            keys = self.redis.keys(f"{self.doc_prefix}*")
            if keys:
                return self.redis.delete(*keys)
            return 0
        except Exception as e:
            print(f"❌ Error clearing vector store: {e}")
            return 0

# Global vector store instance
_vector_store: Optional[EnhancedVectorStore] = None

def get_vector_store() -> EnhancedVectorStore:
    """Get the global vector store instance"""
    global _vector_store
    if _vector_store is None:
        _vector_store = EnhancedVectorStore()
    return _vector_store

def set_vector_store(store: EnhancedVectorStore):
    """Set the global vector store instance"""
    global _vector_store
    _vector_store = store
