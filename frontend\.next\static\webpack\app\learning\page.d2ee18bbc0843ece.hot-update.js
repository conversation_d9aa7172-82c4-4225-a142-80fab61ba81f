"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/learning/page",{

/***/ "(app-pages-browser)/./app/learning/page.tsx":
/*!*******************************!*\
  !*** ./app/learning/page.tsx ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LearningDashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nconst API_BASE = (process.env.NEXT_PUBLIC_API_BASE || \"http://localhost:8000\").replace(/\\/$/, \"\");\nfunction LearningDashboard() {\n    _s();\n    const [userId, setUserId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"test_user\"); // Default user ID - match the one used in backend\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [analytics, setAnalytics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [recommendations, setRecommendations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [userHistory, setUserHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const loadUserData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        setLoading(true);\n        setError(null);\n        try {\n            // Use the new dashboard endpoint to get all data at once\n            const dashboardRes = await fetch(\"\".concat(API_BASE, \"/user/\").concat(userId, \"/dashboard\"));\n            if (dashboardRes.ok) {\n                const dashboardData = await dashboardRes.json();\n                // Set data with fallbacks for empty data\n                setProgress(dashboardData.progress || {\n                    user_id: userId,\n                    total_lessons: 0,\n                    completed_lessons: 0,\n                    completion_rate: 0,\n                    average_score: 0,\n                    total_time_spent: 0\n                });\n                setAnalytics(dashboardData.analytics || {\n                    daily_activity: [],\n                    total_questions: 0,\n                    total_lessons_completed: 0,\n                    average_score: 0,\n                    total_time_spent: 0,\n                    difficulty_distribution: {}\n                });\n                setRecommendations(dashboardData.recommendations || []);\n                setUserHistory(dashboardData.recent_activity || []);\n                // Only show error if there's an actual error in the response\n                if (dashboardData.error) {\n                    console.warn(\"Dashboard data warning:\", dashboardData.error);\n                }\n            } else {\n                throw new Error(\"Dashboard request failed: \".concat(dashboardRes.status));\n            }\n        } catch (e) {\n            console.error(\"Error loading user data:\", e);\n            setError(\"Failed to load user data. Please try asking a question first to generate some data.\");\n        } finally{\n            setLoading(false);\n        }\n    }, [\n        userId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadUserData();\n    }, [\n        loadUserData\n    ]);\n    const formatTime = (minutes)=>{\n        const hours = Math.floor(minutes / 60);\n        const mins = minutes % 60;\n        return hours > 0 ? \"\".concat(hours, \"h \").concat(mins, \"m\") : \"\".concat(mins, \"m\");\n    };\n    const formatDate = (timestamp)=>{\n        return new Date(timestamp).toLocaleDateString();\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                lineNumber: 127,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n            lineNumber: 126,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900\",\n                            children: \"Learning Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-2\",\n                            children: [\n                                \"Personalized learning experience for \",\n                                userId\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 rounded-md border border-red-200 bg-red-50 p-4 text-red-700\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 11\n                }, this),\n                progress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-gray-500\",\n                                    children: \"Total Lessons\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: progress.total_lessons\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-gray-500\",\n                                    children: \"Completed\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-2xl font-bold text-green-600\",\n                                    children: progress.completed_lessons\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-gray-500\",\n                                    children: \"Completion Rate\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-2xl font-bold text-blue-600\",\n                                    children: [\n                                        (progress.completion_rate * 100).toFixed(1),\n                                        \"%\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-gray-500\",\n                                    children: \"Average Score\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-2xl font-bold text-purple-600\",\n                                    children: [\n                                        (progress.average_score * 100).toFixed(1),\n                                        \"%\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 border-b border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: \"Recent Activity\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: userHistory && userHistory.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: userHistory.slice(0, 5).map((activity, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border border-gray-200 rounded-lg p-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium text-gray-900 text-sm\",\n                                                                    children: [\n                                                                        \"Q: \",\n                                                                        activity.question\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                                                                    lineNumber: 187,\n                                                                    columnNumber: 28\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-600 mt-1 line-clamp-2\",\n                                                                    children: activity.answer\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                                                                    lineNumber: 190,\n                                                                    columnNumber: 28\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500 mt-2\",\n                                                                    children: [\n                                                                        new Date(activity.timestamp_ms).toLocaleDateString(),\n                                                                        \" • \",\n                                                                        activity.difficulty || \"N/A\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                                                                    lineNumber: 193,\n                                                                    columnNumber: 28\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 26\n                                                        }, this),\n                                                        activity.score && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"ml-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800\",\n                                                                children: [\n                                                                    (activity.score * 100).toFixed(0),\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                                                                lineNumber: 199,\n                                                                columnNumber: 30\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 28\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 24\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 22\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 18\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500\",\n                                        children: \"No recent activity. Ask a question to see your activity here!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 18\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 26\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 border-b border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: \"Recommended Lessons\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: recommendations.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: recommendations.map((lesson)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border border-gray-200 rounded-lg p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: lesson.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 mt-1\",\n                                                        children: [\n                                                            lesson.subject,\n                                                            \" • \",\n                                                            lesson.difficulty\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 mt-2 line-clamp-2\",\n                                                        children: [\n                                                            lesson.content.substring(0, 150),\n                                                            \"...\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-3 flex gap-2\",\n                                                        children: lesson.tags.slice(0, 3).map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                                                children: tag\n                                                            }, index, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                                                                lineNumber: 233,\n                                                                columnNumber: 27\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, lesson.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500\",\n                                        children: \"No recommendations available\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 border-b border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-lg font-semibold text-gray-900\",\n                                children: \"Learning History\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 14\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 12\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: analytics && analytics.daily_activity && analytics.daily_activity.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: analytics.daily_activity.slice(0, 7).map((day, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border border-gray-200 rounded-lg p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: day.date\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                                                            lineNumber: 263,\n                                                            columnNumber: 26\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: [\n                                                                day.questions_asked,\n                                                                \" questions • \",\n                                                                day.lessons_completed,\n                                                                \" lessons\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                                                            lineNumber: 264,\n                                                            columnNumber: 26\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 24\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: [\n                                                                (day.avg_score * 100).toFixed(1),\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 26\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: formatTime(day.time_spent)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                                                            lineNumber: 272,\n                                                            columnNumber: 26\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 24\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 22\n                                        }, this)\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 20\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 16\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500\",\n                                children: \"No learning history available. Start learning to build your history!\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 16\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 12\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n                    lineNumber: 252,\n                    columnNumber: 10\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n            lineNumber: 134,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive - DataFactZ LLC\\\\Desktop\\\\Redis Project\\\\frontend\\\\app\\\\learning\\\\page.tsx\",\n        lineNumber: 133,\n        columnNumber: 5\n    }, this);\n}\n_s(LearningDashboard, \"YuX8yegCEWNkLOR4/7yNAJSnj4s=\");\n_c = LearningDashboard;\nvar _c;\n$RefreshReg$(_c, \"LearningDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/learning/page.tsx\n"));

/***/ })

});