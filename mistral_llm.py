import requests
import json

def get_answer(prompt: str) -> str:
    """Get answer from Ollama Qwen model"""
    try:
        url = "http://localhost:11434/api/generate"
        payload = {
            "model": "qwen3:8b-q4_K_M",
            "prompt": prompt,
            "stream": False,  # Set to True if you want to stream response
            "options": {
                "temperature": 0.5,  # Lower temperature for more focused responses
                "top_p": 0.8,
                "num_predict": 150,  # Shorter responses for speed
                "top_k": 40,
                "repeat_penalty": 1.1
            }
        }

        print(f"Sending request to Ollama Qwen: {prompt[:50]}...")
        # Increased timeout significantly for model loading and generation
        response = requests.post(url, json=payload, timeout=120)  # Increased to 2 minutes
        print(f"Ollama response status: {response.status_code}")

        if response.status_code == 200:
            result = response.json().get("response", "No response received.")
            print(f"Ollama Qwen response length: {len(result)} characters")
            return result
        else:
            error_msg = f"Error: {response.status_code}, {response.text}"
            print(f"Ollama Qwen error: {error_msg}")
            return error_msg
    except requests.exceptions.ConnectionError:
        error_msg = "Error: Could not connect to Ollama server. Please make sure Ollama is running on localhost:11434 with the qwen3:8b-q4_K_M model"
        print(error_msg)
        return error_msg
    except requests.exceptions.Timeout:
        error_msg = "Error: Request timed out. The Ollama server took too long to respond. This might happen on the first request while the model is loading."
        print(error_msg)
        return error_msg
    except Exception as e:
        error_msg = f"Error: {str(e)}"
        print(f"Unexpected error: {error_msg}")
        return error_msg
