from redis import Redis
from sentence_transformers import SentenceTransformer
import numpy as np
import json
import hashlib
from datetime import datetime

# Connect to Redis with specific configuration
r = Redis(host='localhost', port=6379, db=0, decode_responses=False)

# Load sentence transformer model
model = SentenceTransformer('all-MiniLM-L6-v2')

INDEX_NAME = "questions"
DOC_PREFIX = "question:"

# Step 1: Create the index (do this once)
def create_index():
    try:
        # Check if we have any existing questions
        existing_keys = r.keys(f"{DOC_PREFIX}*")
        if existing_keys:
            print("ℹ️ Questions already exist in Redis.")
        else:
            print("ℹ️ No existing questions found.")
    except Exception as e:
        print(f"⚠️ Error checking Redis: {e}")

# Step 2: Store question and its embedding
def store_question(qid, question_text, embedding_vector, answer_text):
    try:
        # 1. Store query & response using SET with key format "query:{query}"
        query_key = f"query:{question_text}"
        query_data = {
            "query": question_text,
            "response": answer_text,
            "timestamp_ms": int(datetime.utcnow().timestamp() * 1000)
        }
        r.set(query_key, json.dumps(query_data).encode('utf-8'))
        
        # 2. Push JSON object to Redis list named "history"
        history_item = {
            "timestamp_ms": int(datetime.utcnow().timestamp() * 1000),
            "query": question_text,
            "response": answer_text
        }
        r.lpush("history", json.dumps(history_item).encode('utf-8'))
        
        # 3. Store as JSON string for vector search (existing functionality)
        data = {
            "text": answer_text,
            "question": question_text,
            "embedding": embedding_vector.tolist()  # Convert numpy array to list
        }
        
        r.set(f"{DOC_PREFIX}{qid}", json.dumps(data).encode('utf-8'))
        print(f"✅ Stored question and answer: {question_text[:50]}...")
    except Exception as e:
        print(f"❌ Error storing question: {e}")

# Step 3: Semantic search for similar questions (updated with exact cache check)
def search_similar(query, top_k=3):
    try:
        # 1. First check if exact query key exists in Redis
        query_key = f"query:{query}"
        cached_data = r.get(query_key)
        
        if cached_data:
            try:
                data = json.loads(cached_data.decode('utf-8'))
                response = data.get('response', '')
                print(f"✅ Exact cache hit for query: {query[:50]}...")
                return [(1.0, f"[From Redis Cache] {response}")]  # Return with prefix and perfect score
            except Exception as e:
                print(f"⚠️ Error parsing cached data: {e}")
        
        # 2. If not found, perform vector search
        print("🔍 No exact cache hit, performing vector search...")
        
        # Get all stored questions
        keys = r.keys(f"{DOC_PREFIX}*")
        if not keys:
            return []
        
        # Encode the query
        query_vector = model.encode(query).astype(np.float32)
        
        results = []
        for key in keys:
            try:
                # Get stored data
                data_bytes = r.get(key)
                if data_bytes:
                    data = json.loads(data_bytes.decode('utf-8'))
                    stored_embedding = np.array(data['embedding'], dtype=np.float32)
                    
                    # Calculate cosine similarity
                    similarity = np.dot(query_vector, stored_embedding) / (
                        np.linalg.norm(query_vector) * np.linalg.norm(stored_embedding)
                    )
                    
                    results.append((similarity, data['text']))
            except Exception as e:
                print(f"⚠️ Error processing key {key}: {e}")
                continue
        
        # Sort by similarity and return top_k
        results.sort(key=lambda x: x[0], reverse=True)
        return results[:top_k]
        
    except Exception as e:
        print(f"❌ Error in search: {e}")
        return []

# New function to get cached response directly
def get_cached_response(query):
    """Get cached response for exact query match"""
    try:
        query_key = f"query:{query}"
        cached_data = r.get(query_key)
        
        if cached_data:
            data = json.loads(cached_data.decode('utf-8'))
            return data.get('response', '')
        return ""  # Return empty string instead of None
    except Exception as e:
        print(f"❌ Error getting cached response: {e}")
        return ""  # Return empty string instead of None

# New function to add to history
def add_to_history(query, response):
    """Add query and response to history list"""
    try:
        history_item = {
            "timestamp_ms": int(datetime.utcnow().timestamp() * 1000),
            "query": query,
            "response": response
        }
        r.lpush("history", json.dumps(history_item).encode('utf-8'))
        print(f"✅ Added to history: {query[:50]}...")
    except Exception as e:
        print(f"❌ Error adding to history: {e}")
