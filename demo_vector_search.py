#!/usr/bin/env python3
"""
Demo script for Redis Vector Embeddings system
Showcases the key functionality with sample data
"""

import requests
import json
import time
from typing import List, Dict, Any

# API base URL
BASE_URL = "http://localhost:8000"

def check_api_health():
    """Check if the API is running"""
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def store_sample_qa_pairs():
    """Store sample Q&A pairs for demonstration"""
    sample_qa_pairs = [
        {
            "question": "What is machine learning?",
            "answer": "Machine learning is a subset of artificial intelligence that enables computers to learn and improve from experience without being explicitly programmed. It uses algorithms to analyze data, identify patterns, and make predictions or decisions.",
            "subject": "AI",
            "difficulty": "beginner",
            "metadata": {"source": "textbook", "chapter": "introduction"}
        },
        {
            "question": "How do neural networks work?",
            "answer": "Neural networks are computing systems inspired by biological neural networks. They consist of interconnected nodes (neurons) organized in layers. Each connection has a weight, and the network learns by adjusting these weights based on training data to minimize prediction errors.",
            "subject": "AI",
            "difficulty": "intermediate",
            "metadata": {"source": "research_paper", "topic": "deep_learning"}
        },
        {
            "question": "What is supervised learning?",
            "answer": "Supervised learning is a type of machine learning where algorithms learn from labeled training data. The algorithm learns to map inputs to correct outputs by training on examples where the desired output is known, then applies this learning to make predictions on new, unseen data.",
            "subject": "AI",
            "difficulty": "beginner",
            "metadata": {"source": "course", "module": "ml_basics"}
        },
        {
            "question": "Explain deep learning",
            "answer": "Deep learning is a subset of machine learning that uses artificial neural networks with multiple layers (hence 'deep') to model and understand complex patterns in data. It's particularly effective for tasks like image recognition, natural language processing, and speech recognition.",
            "subject": "AI",
            "difficulty": "intermediate",
            "metadata": {"source": "tutorial", "topic": "advanced_ml"}
        },
        {
            "question": "What is Python?",
            "answer": "Python is a high-level, interpreted programming language known for its simple syntax and readability. It's widely used for web development, data science, artificial intelligence, automation, and many other applications.",
            "subject": "Programming",
            "difficulty": "beginner",
            "metadata": {"source": "documentation", "language": "python"}
        }
    ]
    
    print("📚 Storing sample Q&A pairs...")
    stored_ids = []
    
    for qa in sample_qa_pairs:
        try:
            response = requests.post(f"{BASE_URL}/vector/store/qa", json=qa)
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    stored_ids.append(result["document_id"])
                    print(f"✅ Stored: {qa['question'][:50]}...")
                else:
                    print(f"❌ Failed to store: {qa['question'][:50]}...")
            else:
                print(f"❌ HTTP {response.status_code}: {qa['question'][:50]}...")
        except Exception as e:
            print(f"❌ Error storing Q&A: {e}")
    
    print(f"📊 Successfully stored {len(stored_ids)} Q&A pairs")
    return stored_ids

def demonstrate_vector_search():
    """Demonstrate vector similarity search"""
    print("\n🔍 Demonstrating Vector Search...")
    
    search_queries = [
        "Tell me about ML",
        "How do AI networks function?",
        "What is programming?",
        "Explain artificial intelligence"
    ]
    
    for query in search_queries:
        print(f"\n🔎 Searching for: '{query}'")
        
        try:
            response = requests.post(f"{BASE_URL}/vector/search/qa", json={
                "question": query,
                "similarity_threshold": 0.6,
                "max_results": 3,
                "include_context": True
            })
            
            if response.status_code == 200:
                results = response.json()
                similar_qa = results.get("similar_qa_pairs", [])
                
                if similar_qa:
                    print(f"📋 Found {len(similar_qa)} similar questions:")
                    for i, qa in enumerate(similar_qa, 1):
                        print(f"  {i}. Q: {qa['question']}")
                        print(f"     A: {qa['answer'][:100]}...")
                        print(f"     Similarity: {qa['similarity_score']:.3f}")
                        print(f"     Subject: {qa.get('metadata', {}).get('subject', 'N/A')}")
                        print()
                else:
                    print("  No similar questions found")
            else:
                print(f"  ❌ Search failed: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"  ❌ Search error: {e}")

def demonstrate_enhanced_ask():
    """Demonstrate enhanced ask with context"""
    print("\n🤖 Demonstrating Enhanced Ask...")
    
    questions = [
        "What's the difference between ML and deep learning?",
        "Can you explain neural networks in simple terms?",
        "How is Python used in AI?"
    ]
    
    for question in questions:
        print(f"\n❓ Question: {question}")
        
        try:
            response = requests.post(f"{BASE_URL}/ask/enhanced", json={
                "query": question,
                "user_id": "demo_user"
            })
            
            if response.status_code == 200:
                result = response.json()
                print(f"💡 Answer ({result['source']}): {result['answer'][:200]}...")
            else:
                print(f"❌ Failed: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error: {e}")

def show_vector_stats():
    """Show vector store statistics"""
    print("\n📊 Vector Store Statistics...")
    
    try:
        response = requests.get(f"{BASE_URL}/vector/stats")
        if response.status_code == 200:
            stats = response.json()
            print(f"📈 Total Documents: {stats.get('total_documents', 0)}")
            print(f"🔤 Q&A Pairs: {stats.get('qa_pairs', 0)}")
            print(f"🧠 Embedding Provider: {stats.get('embedding_provider', {}).get('provider', 'Unknown')}")
            print(f"📏 Model: {stats.get('embedding_provider', {}).get('model_name', 'Unknown')}")
            print(f"🔢 Dimensions: {stats.get('embedding_provider', {}).get('dimensions', 'Unknown')}")
        else:
            print(f"❌ Failed to get stats: HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ Stats error: {e}")

def demonstrate_search_suggestions():
    """Demonstrate search suggestions"""
    print("\n💡 Demonstrating Search Suggestions...")
    
    partial_queries = ["machine", "neural", "python"]
    
    for query in partial_queries:
        print(f"\n🔍 Suggestions for: '{query}'")
        
        try:
            response = requests.get(f"{BASE_URL}/vector/search/suggestions", params={
                "query": query,
                "limit": 3
            })
            
            if response.status_code == 200:
                result = response.json()
                suggestions = result.get("suggestions", [])
                
                if suggestions:
                    for i, suggestion in enumerate(suggestions, 1):
                        print(f"  {i}. {suggestion['question']} (similarity: {suggestion['similarity']:.3f})")
                else:
                    print("  No suggestions found")
            else:
                print(f"  ❌ Failed: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"  ❌ Error: {e}")

def main():
    """Main demo function"""
    print("🚀 Redis Vector Embeddings Demo")
    print("=" * 50)
    
    # Check if API is running
    if not check_api_health():
        print("❌ API is not running. Please start the server with: python main.py")
        return
    
    print("✅ API is running")
    
    # Store sample data
    stored_ids = store_sample_qa_pairs()
    
    if not stored_ids:
        print("❌ No data was stored. Cannot proceed with demo.")
        return
    
    # Wait a moment for data to be indexed
    print("\n⏳ Waiting for data to be indexed...")
    time.sleep(2)
    
    # Show statistics
    show_vector_stats()
    
    # Demonstrate vector search
    demonstrate_vector_search()
    
    # Demonstrate enhanced ask
    demonstrate_enhanced_ask()
    
    # Demonstrate search suggestions
    demonstrate_search_suggestions()
    
    print("\n🎉 Demo completed!")
    print("\n📖 Next steps:")
    print("1. Visit http://localhost:8000/docs for interactive API documentation")
    print("2. Try the /vector/search/qa endpoint with your own questions")
    print("3. Use /ask/enhanced for intelligent Q&A with context")
    print("4. Configure different embedding providers in .env file")

if __name__ == "__main__":
    main()
