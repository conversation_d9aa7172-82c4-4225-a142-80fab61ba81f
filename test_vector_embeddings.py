"""
Comprehensive tests for Redis Vector Embeddings system
"""

import pytest
import numpy as np
import json
from unittest.mock import Mock, patch, MagicMock
from redis import Redis
import tempfile
import os

# Import modules to test
from embedding_service import (
    EmbeddingService, 
    EmbeddingServiceFactory, 
    EmbeddingProvider, 
    EmbeddingConfig
)
from enhanced_vector_store import (
    EnhancedVectorStore, 
    VectorDocument, 
    SearchResult, 
    SearchConfig
)
from config import AppConfig, RedisConfig, EmbeddingConfig as ConfigEmbeddingConfig

class TestEmbeddingService:
    """Test cases for EmbeddingService"""
    
    def test_sentence_transformer_initialization(self):
        """Test Sentence Transformers initialization"""
        config = EmbeddingConfig(
            provider=EmbeddingProvider.SENTENCE_TRANSFORMERS,
            model_name="all-MiniLM-L6-v2"
        )
        
        with patch('embedding_service.SentenceTransformer') as mock_st:
            mock_model = Mock()
            mock_model.encode.return_value = np.array([0.1, 0.2, 0.3])
            mock_st.return_value = mock_model
            
            service = EmbeddingService(config)
            
            assert service.provider == EmbeddingProvider.SENTENCE_TRANSFORMERS
            assert service.config.model_name == "all-MiniLM-L6-v2"
            mock_st.assert_called_once_with("all-MiniLM-L6-v2")
    
    def test_openai_initialization(self):
        """Test OpenAI initialization"""
        config = EmbeddingConfig(
            provider=EmbeddingProvider.OPENAI,
            model_name="text-embedding-3-small",
            api_key="test-key"
        )
        
        with patch('embedding_service.openai.OpenAI') as mock_openai:
            mock_client = Mock()
            mock_response = Mock()
            mock_response.data = [Mock(embedding=[0.1, 0.2, 0.3])]
            mock_client.embeddings.create.return_value = mock_response
            mock_openai.return_value = mock_client
            
            service = EmbeddingService(config)
            
            assert service.provider == EmbeddingProvider.OPENAI
            mock_openai.assert_called_once_with(api_key="test-key")
    
    def test_get_embedding_sentence_transformers(self):
        """Test getting embeddings with Sentence Transformers"""
        config = EmbeddingConfig(
            provider=EmbeddingProvider.SENTENCE_TRANSFORMERS,
            model_name="all-MiniLM-L6-v2"
        )
        
        with patch('embedding_service.SentenceTransformer') as mock_st:
            mock_model = Mock()
            test_embedding = np.array([0.1, 0.2, 0.3], dtype=np.float32)
            mock_model.encode.return_value = [test_embedding]
            mock_st.return_value = mock_model
            
            service = EmbeddingService(config)
            result = service.get_embedding("test text")
            
            np.testing.assert_array_equal(result, test_embedding)
            mock_model.encode.assert_called_with(["test text"], convert_to_numpy=True)
    
    def test_factory_create_sentence_transformer(self):
        """Test factory method for Sentence Transformers"""
        with patch('embedding_service.SentenceTransformer'):
            service = EmbeddingServiceFactory.create_sentence_transformer_service()
            assert service.provider == EmbeddingProvider.SENTENCE_TRANSFORMERS
            assert service.config.model_name == "all-MiniLM-L6-v2"
    
    def test_factory_create_from_env(self):
        """Test factory method from environment variables"""
        with patch.dict(os.environ, {
            'EMBEDDING_PROVIDER': 'sentence_transformers',
            'EMBEDDING_MODEL': 'test-model'
        }):
            with patch('embedding_service.SentenceTransformer'):
                service = EmbeddingServiceFactory.create_from_env()
                assert service.provider == EmbeddingProvider.SENTENCE_TRANSFORMERS
                assert service.config.model_name == "test-model"

class TestEnhancedVectorStore:
    """Test cases for EnhancedVectorStore"""
    
    @pytest.fixture
    def mock_redis(self):
        """Mock Redis client"""
        mock_redis = Mock(spec=Redis)
        mock_redis.ping.return_value = True
        mock_redis.keys.return_value = []
        return mock_redis
    
    @pytest.fixture
    def mock_embedding_service(self):
        """Mock embedding service"""
        mock_service = Mock()
        mock_service.get_embedding.return_value = np.array([0.1, 0.2, 0.3], dtype=np.float32)
        mock_service.get_provider_info.return_value = {
            "provider": "test",
            "model_name": "test-model",
            "dimensions": 3
        }
        return mock_service
    
    def test_vector_store_initialization(self, mock_redis, mock_embedding_service):
        """Test vector store initialization"""
        store = EnhancedVectorStore(
            redis_client=mock_redis,
            embedding_service=mock_embedding_service
        )
        
        assert store.redis == mock_redis
        assert store.embedding_service == mock_embedding_service
        mock_redis.ping.assert_called_once()
    
    def test_store_document(self, mock_redis, mock_embedding_service):
        """Test storing a document"""
        mock_redis.set.return_value = True
        
        store = EnhancedVectorStore(
            redis_client=mock_redis,
            embedding_service=mock_embedding_service
        )
        
        doc_id = store.store_document("test text", {"key": "value"})
        
        assert doc_id is not None
        assert len(doc_id) == 16  # SHA256 hash truncated to 16 chars
        mock_embedding_service.get_embedding.assert_called_once_with("test text")
        assert mock_redis.set.call_count >= 1
    
    def test_store_qa_pair(self, mock_redis, mock_embedding_service):
        """Test storing a Q&A pair"""
        mock_redis.set.return_value = True
        
        store = EnhancedVectorStore(
            redis_client=mock_redis,
            embedding_service=mock_embedding_service
        )
        
        doc_id = store.store_qa_pair("What is AI?", "AI is artificial intelligence")
        
        assert doc_id is not None
        mock_embedding_service.get_embedding.assert_called_once_with("What is AI?")
    
    def test_search_similar(self, mock_redis, mock_embedding_service):
        """Test similarity search"""
        # Mock stored document
        stored_doc = VectorDocument(
            id="test-id",
            text="test query",
            embedding=[0.1, 0.2, 0.3],
            metadata={"type": "qa_pair"},
            timestamp=1234567890
        )
        
        mock_redis.keys.return_value = [b"doc:test-id"]
        mock_redis.get.return_value = json.dumps(stored_doc.to_dict()).encode('utf-8')
        
        store = EnhancedVectorStore(
            redis_client=mock_redis,
            embedding_service=mock_embedding_service
        )
        
        results = store.search_similar("test query")
        
        assert len(results) == 1
        assert isinstance(results[0], SearchResult)
        assert results[0].document.id == "test-id"
        assert results[0].similarity_score > 0.9  # Should be very similar
    
    def test_get_stats(self, mock_redis, mock_embedding_service):
        """Test getting vector store statistics"""
        mock_redis.keys.return_value = [b"doc:1", b"doc:2"]
        
        store = EnhancedVectorStore(
            redis_client=mock_redis,
            embedding_service=mock_embedding_service
        )
        
        stats = store.get_stats()
        
        assert "total_documents" in stats
        assert "embedding_provider" in stats
        assert stats["redis_connected"] is True

class TestConfig:
    """Test cases for configuration management"""
    
    def test_redis_config_from_env(self):
        """Test Redis configuration from environment"""
        with patch.dict(os.environ, {
            'REDIS_HOST': 'test-host',
            'REDIS_PORT': '1234',
            'REDIS_DB': '5'
        }):
            config = RedisConfig.from_env()
            assert config.host == "test-host"
            assert config.port == 1234
            assert config.db == 5
    
    def test_embedding_config_from_env(self):
        """Test embedding configuration from environment"""
        with patch.dict(os.environ, {
            'EMBEDDING_PROVIDER': 'openai',
            'EMBEDDING_MODEL': 'text-embedding-3-small',
            'OPENAI_API_KEY': 'test-key'
        }):
            config = ConfigEmbeddingConfig.from_env()
            assert config.provider == "openai"
            assert config.model_name == "text-embedding-3-small"
            assert config.api_key == "test-key"
    
    def test_app_config_validation(self):
        """Test application configuration validation"""
        from config import validate_config
        
        # Test invalid configuration
        config = AppConfig()
        config.embedding.provider = "openai"
        config.embedding.api_key = None  # Missing API key
        
        errors = validate_config(config)
        assert len(errors) > 0
        assert any("OpenAI API key" in error for error in errors)

class TestIntegration:
    """Integration tests"""
    
    @pytest.fixture
    def temp_redis(self):
        """Create a temporary Redis-like storage for testing"""
        return {}
    
    def test_end_to_end_qa_storage_and_search(self, temp_redis):
        """Test complete Q&A storage and search workflow"""
        # Mock Redis operations
        with patch('redis.Redis') as mock_redis_class:
            mock_redis = Mock()
            mock_redis.ping.return_value = True
            mock_redis.keys.return_value = []
            mock_redis.set.return_value = True
            mock_redis.get.return_value = None
            mock_redis_class.return_value = mock_redis
            
            # Mock embedding service
            with patch('embedding_service.SentenceTransformer') as mock_st:
                mock_model = Mock()
                mock_model.encode.return_value = np.array([0.1, 0.2, 0.3])
                mock_st.return_value = mock_model
                
                # Create services
                embedding_service = EmbeddingServiceFactory.create_sentence_transformer_service()
                vector_store = EnhancedVectorStore(
                    redis_client=mock_redis,
                    embedding_service=embedding_service
                )
                
                # Store a Q&A pair
                doc_id = vector_store.store_qa_pair(
                    "What is machine learning?",
                    "Machine learning is a subset of AI that enables computers to learn without explicit programming."
                )
                
                assert doc_id is not None
                mock_redis.set.assert_called()

if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v"])
