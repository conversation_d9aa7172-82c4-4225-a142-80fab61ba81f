"use client";

import { useCallback, useEffect, useState } from "react";

type UserProgress = {
  user_id: string;
  total_lessons: number;
  completed_lessons: number;
  completion_rate: number;
  average_score: number;
  total_time_spent: number;
};

type UserAnalytics = {
  daily_activity: Array<{
    date: string;
    questions_asked: number;
    lessons_completed: number;
    avg_score: number;
    time_spent: number;
  }>;
  total_questions: number;
  total_lessons_completed: number;
  average_score: number;
  total_time_spent: number;
  difficulty_distribution: Record<string, number>;
};

type Lesson = {
  id: string;
  title: string;
  content: string;
  difficulty: string;
  subject: string;
  tags: string[];
  created_at: number;
  similarity?: number;
};

type UserHistory = {
  timestamp_ms: number;
  question?: string;
  answer?: string;
  lesson_id?: string;
  difficulty?: string;
  score?: number;
  type: string;
};

const API_BASE = (process.env.NEXT_PUBLIC_API_BASE || "http://localhost:8000").replace(/\/$/, "");

export default function LearningDashboard() {
  const [userId, setUserId] = useState("test_user"); // Default user ID - match the one used in backend
  const [progress, setProgress] = useState<UserProgress | null>(null);
  const [analytics, setAnalytics] = useState<UserAnalytics | null>(null);
  const [recommendations, setRecommendations] = useState<Lesson[]>([]);
  const [userHistory, setUserHistory] = useState<UserHistory[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadUserData = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Use the new dashboard endpoint to get all data at once
      const dashboardRes = await fetch(`${API_BASE}/user/${userId}/dashboard`);
      
      if (dashboardRes.ok) {
        const dashboardData = await dashboardRes.json();
        
        // Set data with fallbacks for empty data
        setProgress(dashboardData.progress || {
          user_id: userId,
          total_lessons: 0,
          completed_lessons: 0,
          completion_rate: 0,
          average_score: 0,
          total_time_spent: 0
        });
        
        setAnalytics(dashboardData.analytics || {
          daily_activity: [],
          total_questions: 0,
          total_lessons_completed: 0,
          average_score: 0,
          total_time_spent: 0,
          difficulty_distribution: {}
        });
        
        setRecommendations(dashboardData.recommendations || []);
        setUserHistory(dashboardData.recent_activity || []);
        
        // Only show error if there's an actual error in the response
        if (dashboardData.error) {
          console.warn("Dashboard data warning:", dashboardData.error);
        }
      } else {
        throw new Error(`Dashboard request failed: ${dashboardRes.status}`);
      }

    } catch (e: any) {
      console.error("Error loading user data:", e);
      setError("Failed to load user data. Please try asking a question first to generate some data.");
    } finally {
      setLoading(false);
    }
  }, [userId]);

  useEffect(() => {
    loadUserData();
  }, [loadUserData]);

  const formatTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Learning Dashboard</h1>
          <p className="text-gray-600 mt-2">Personalized learning experience for {userId}</p>
          
          {/* User ID Input for Testing */}
          <div className="mt-4 flex items-center space-x-2">
            <label htmlFor="userId" className="text-sm font-medium text-gray-700">User ID:</label>
            <input
              id="userId"
              type="text"
              value={userId}
              onChange={(e) => setUserId(e.target.value)}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter user ID"
            />
            <button
              onClick={loadUserData}
              className="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              Load Data
            </button>
          </div>
        </div>

        {error && (
          <div className="mb-6 rounded-md border border-red-200 bg-red-50 p-4 text-red-700">
            {error}
          </div>
        )}

        {/* Progress Overview */}
        {progress && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-sm font-medium text-gray-500">Total Lessons</h3>
              <p className="text-2xl font-bold text-gray-900">{progress.total_lessons}</p>
            </div>
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-sm font-medium text-gray-500">Completed</h3>
              <p className="text-2xl font-bold text-green-600">{progress.completed_lessons}</p>
            </div>
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-sm font-medium text-gray-500">Completion Rate</h3>
              <p className="text-2xl font-bold text-blue-600">
                {(progress.completion_rate * 100).toFixed(1)}%
              </p>
            </div>
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-sm font-medium text-gray-500">Average Score</h3>
              <p className="text-2xl font-bold text-purple-600">
                {(progress.average_score * 100).toFixed(1)}%
              </p>
            </div>
          </div>
        )}

        {/* Analytics and Recommendations */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Recent Activity */}
          <div className="bg-white rounded-lg shadow">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Recent Activity</h2>
            </div>
                         <div className="p-6">
               {userHistory && userHistory.length > 0 ? (
                 <div className="space-y-4">
                   {userHistory.slice(0, 5).map((activity, index) => (
                     <div key={index} className="border border-gray-200 rounded-lg p-3">
                       <div className="flex justify-between items-start">
                         <div className="flex-1">
                           <p className="font-medium text-gray-900 text-sm">
                             Q: {activity.question}
                           </p>
                           <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                             {activity.answer}
                           </p>
                           <p className="text-xs text-gray-500 mt-2">
                             {new Date(activity.timestamp_ms).toLocaleDateString()} • {activity.difficulty || 'N/A'}
                           </p>
                         </div>
                         {activity.score && (
                           <div className="ml-2">
                             <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                               {(activity.score * 100).toFixed(0)}%
                             </span>
                           </div>
                         )}
                       </div>
                     </div>
                   ))}
                 </div>
               ) : (
                 <p className="text-gray-500">No recent activity. Ask a question to see your activity here!</p>
               )}
             </div>
          </div>

          {/* Recommended Lessons */}
          <div className="bg-white rounded-lg shadow">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Recommended Lessons</h2>
            </div>
            <div className="p-6">
              {recommendations.length > 0 ? (
                <div className="space-y-4">
                  {recommendations.map((lesson) => (
                    <div key={lesson.id} className="border border-gray-200 rounded-lg p-4">
                      <h3 className="font-medium text-gray-900">{lesson.title}</h3>
                      <p className="text-sm text-gray-500 mt-1">
                        {lesson.subject} • {lesson.difficulty}
                      </p>
                      <p className="text-sm text-gray-600 mt-2 line-clamp-2">
                        {lesson.content.substring(0, 150)}...
                      </p>
                      <div className="mt-3 flex gap-2">
                        {lesson.tags.slice(0, 3).map((tag, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500">No recommendations available</p>
              )}
            </div>
          </div>
        </div>

                 {/* Learning History */}
         <div className="bg-white rounded-lg shadow">
           <div className="p-6 border-b border-gray-200">
             <h2 className="text-lg font-semibold text-gray-900">Learning History</h2>
           </div>
           <div className="p-6">
             {analytics && analytics.daily_activity && analytics.daily_activity.length > 0 ? (
               <div className="space-y-4">
                 {analytics.daily_activity.slice(0, 7).map((day, index) => (
                   <div key={index} className="border border-gray-200 rounded-lg p-4">
                     <div className="flex justify-between items-center">
                       <div className="flex-1">
                         <p className="font-medium text-gray-900">{day.date}</p>
                         <p className="text-sm text-gray-500">
                           {day.questions_asked} questions • {day.lessons_completed} lessons
                         </p>
                       </div>
                       <div className="text-right">
                         <p className="font-medium text-gray-900">
                           {(day.avg_score * 100).toFixed(1)}%
                         </p>
                         <p className="text-sm text-gray-500">
                           {formatTime(day.time_spent)}
                         </p>
                       </div>
                     </div>
                   </div>
                 ))}
               </div>
             ) : (
               <p className="text-gray-500">No learning history available. Start learning to build your history!</p>
             )}
           </div>
         </div>
         
         {/* Debug Section - Remove this in production */}
         <div className="mt-8 bg-gray-100 rounded-lg p-6">
           <h2 className="text-lg font-semibold text-gray-900 mb-4">Debug Information</h2>
           <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
             <div>
               <h3 className="font-medium text-gray-700">Current User ID:</h3>
               <p className="text-gray-600">{userId}</p>
             </div>
             <div>
               <h3 className="font-medium text-gray-700">API Base URL:</h3>
               <p className="text-gray-600">{API_BASE}</p>
             </div>
             <div>
               <h3 className="font-medium text-gray-700">Data Status:</h3>
               <p className="text-gray-600">
                 Progress: {progress ? 'Loaded' : 'Not loaded'} | 
                 Analytics: {analytics ? 'Loaded' : 'Not loaded'} | 
                 History: {userHistory ? `${userHistory.length} items` : 'Not loaded'}
               </p>
             </div>
             <div>
               <h3 className="font-medium text-gray-700">Last Error:</h3>
               <p className="text-gray-600">{error || 'None'}</p>
             </div>
           </div>
         </div>
      </div>
    </div>
  );
}
