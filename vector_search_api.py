"""
Advanced Vector Search API endpoints for Redis vector embeddings
"""

from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Query, HTTPException, Body
from pydantic import BaseModel, Field
from datetime import datetime, timedelta
import json

from enhanced_vector_store import (
    get_vector_store, 
    EnhancedVectorStore, 
    SearchConfig, 
    SearchResult
)
from embedding_service import get_embedding_service

# Create router for vector search endpoints
router = APIRouter(prefix="/vector", tags=["Vector Search"])

class VectorSearchRequest(BaseModel):
    query: str = Field(..., description="Search query text")
    similarity_threshold: float = Field(0.7, ge=0.0, le=1.0, description="Minimum similarity threshold")
    max_results: int = Field(10, ge=1, le=50, description="Maximum number of results")
    include_metadata: bool = Field(True, description="Include document metadata in results")
    filter_metadata: Optional[Dict[str, Any]] = Field(None, description="Filter results by metadata")

class VectorSearchResponse(BaseModel):
    query: str
    results: List[Dict[str, Any]]
    total_found: int
    search_time_ms: int
    embedding_provider: Dict[str, Any]

class QASearchRequest(BaseModel):
    question: str = Field(..., description="Question to search for similar Q&A pairs")
    similarity_threshold: float = Field(0.75, ge=0.0, le=1.0, description="Minimum similarity threshold")
    max_results: int = Field(5, ge=1, le=20, description="Maximum number of results")
    include_context: bool = Field(True, description="Include additional context in results")
    subject_filter: Optional[str] = Field(None, description="Filter by subject/topic")
    difficulty_filter: Optional[str] = Field(None, description="Filter by difficulty level")

class QASearchResponse(BaseModel):
    question: str
    similar_qa_pairs: List[Dict[str, Any]]
    total_found: int
    search_time_ms: int
    recommendations: List[str] = Field(default_factory=list)

class StoreQARequest(BaseModel):
    question: str = Field(..., description="Question text")
    answer: str = Field(..., description="Answer text")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
    user_id: Optional[str] = Field(None, description="User ID who asked the question")
    subject: Optional[str] = Field(None, description="Subject/topic")
    difficulty: Optional[str] = Field("beginner", description="Difficulty level")

class StoreQAResponse(BaseModel):
    success: bool
    document_id: str
    message: str

@router.post("/search", response_model=VectorSearchResponse)
async def vector_search(request: VectorSearchRequest):
    """Advanced vector similarity search"""
    start_time = datetime.now()
    
    try:
        vector_store = get_vector_store()
        embedding_service = get_embedding_service()
        
        # Create search configuration
        config = SearchConfig(
            similarity_threshold=request.similarity_threshold,
            max_results=request.max_results,
            include_metadata=request.include_metadata,
            filter_metadata=request.filter_metadata
        )
        
        # Perform search
        results = vector_store.search_similar(request.query, config)
        
        # Format results
        formatted_results = []
        for result in results:
            formatted_result = result.to_dict()
            if not request.include_metadata:
                formatted_result.pop('metadata', None)
            formatted_results.append(formatted_result)
        
        search_time = int((datetime.now() - start_time).total_seconds() * 1000)
        
        return VectorSearchResponse(
            query=request.query,
            results=formatted_results,
            total_found=len(results),
            search_time_ms=search_time,
            embedding_provider=embedding_service.get_provider_info()
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")

@router.post("/search/qa", response_model=QASearchResponse)
async def search_qa_pairs(request: QASearchRequest):
    """Search for similar question-answer pairs with enhanced context"""
    start_time = datetime.now()
    
    try:
        vector_store = get_vector_store()
        
        # Build metadata filter
        filter_metadata = {"type": "qa_pair"}
        if request.subject_filter:
            filter_metadata["subject"] = request.subject_filter
        if request.difficulty_filter:
            filter_metadata["difficulty"] = request.difficulty_filter
        
        # Create search configuration
        config = SearchConfig(
            similarity_threshold=request.similarity_threshold,
            max_results=request.max_results,
            include_metadata=True,
            filter_metadata=filter_metadata
        )
        
        # Perform search
        qa_results = vector_store.search_qa_pairs(request.question, config)
        
        # Add context and recommendations
        recommendations = []
        if qa_results:
            # Generate recommendations based on search results
            top_result = qa_results[0]
            if top_result["similarity_score"] > 0.9:
                recommendations.append("Very similar question found - consider using cached answer")
            elif top_result["similarity_score"] > 0.8:
                recommendations.append("Similar question found - review and adapt answer")
            else:
                recommendations.append("Related questions found - use as context for new answer")
            
            # Add subject-based recommendations
            subjects = set()
            for result in qa_results[:3]:
                subject = result.get("metadata", {}).get("subject")
                if subject:
                    subjects.add(subject)
            
            if subjects:
                recommendations.append(f"Related subjects: {', '.join(subjects)}")
        
        search_time = int((datetime.now() - start_time).total_seconds() * 1000)
        
        return QASearchResponse(
            question=request.question,
            similar_qa_pairs=qa_results,
            total_found=len(qa_results),
            search_time_ms=search_time,
            recommendations=recommendations
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"QA search failed: {str(e)}")

@router.post("/store/qa", response_model=StoreQAResponse)
async def store_qa_pair(request: StoreQARequest):
    """Store a new question-answer pair with vector embedding"""
    try:
        vector_store = get_vector_store()
        
        # Prepare metadata
        metadata = {
            "type": "qa_pair",
            "question": request.question,
            "answer": request.answer,
            "subject": request.subject or "general",
            "difficulty": request.difficulty,
            "created_at": datetime.utcnow().isoformat(),
            **(request.metadata or {})
        }
        
        if request.user_id:
            metadata["user_id"] = request.user_id
        
        # Store the QA pair
        doc_id = vector_store.store_qa_pair(
            question=request.question,
            answer=request.answer,
            metadata=metadata
        )
        
        return StoreQAResponse(
            success=True,
            document_id=doc_id,
            message=f"Successfully stored QA pair with ID: {doc_id}"
        )
        
    except Exception as e:
        return StoreQAResponse(
            success=False,
            document_id="",
            message=f"Failed to store QA pair: {str(e)}"
        )

@router.get("/stats")
async def get_vector_stats():
    """Get vector store statistics and health information"""
    try:
        vector_store = get_vector_store()
        embedding_service = get_embedding_service()
        
        stats = vector_store.get_stats()
        stats["embedding_service"] = embedding_service.get_provider_info()
        stats["timestamp"] = datetime.utcnow().isoformat()
        
        return stats
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get stats: {str(e)}")

@router.get("/search/suggestions")
async def get_search_suggestions(
    query: str = Query(..., description="Partial query for suggestions"),
    limit: int = Query(5, ge=1, le=20, description="Maximum number of suggestions")
):
    """Get search suggestions based on stored questions"""
    try:
        vector_store = get_vector_store()
        
        # Search for similar questions with lower threshold for suggestions
        config = SearchConfig(
            similarity_threshold=0.5,
            max_results=limit,
            filter_metadata={"type": "qa_pair"}
        )
        
        results = vector_store.search_similar(query, config)
        
        suggestions = []
        for result in results:
            metadata = result.document.metadata
            question = metadata.get("question", result.document.text)
            suggestions.append({
                "question": question,
                "similarity": result.similarity_score,
                "subject": metadata.get("subject", "general")
            })
        
        return {
            "query": query,
            "suggestions": suggestions,
            "count": len(suggestions)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get suggestions: {str(e)}")

@router.delete("/documents/{doc_id}")
async def delete_document(doc_id: str):
    """Delete a document from the vector store"""
    try:
        vector_store = get_vector_store()
        success = vector_store.delete_document(doc_id)
        
        if success:
            return {"success": True, "message": f"Document {doc_id} deleted successfully"}
        else:
            raise HTTPException(status_code=404, detail=f"Document {doc_id} not found")
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to delete document: {str(e)}")

@router.get("/health")
async def health_check():
    """Health check for vector search service"""
    try:
        vector_store = get_vector_store()
        embedding_service = get_embedding_service()
        
        # Test embedding generation
        test_embedding = embedding_service.get_embedding("test")
        
        return {
            "status": "healthy",
            "vector_store": "connected",
            "embedding_service": embedding_service.get_provider_info(),
            "embedding_dimensions": len(test_embedding) if test_embedding.size > 0 else 0,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"Service unhealthy: {str(e)}")
