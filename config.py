"""
Configuration management for Redis Vector Embeddings system
"""

import os
from typing import Optional, Dict, Any, List
from dataclasses import dataclass, field
from dotenv import load_dotenv
import json

# Load environment variables
load_dotenv()

@dataclass
class RedisConfig:
    """Redis connection configuration"""
    host: str = "localhost"
    port: int = 6379
    db: int = 0
    password: Optional[str] = None
    decode_responses: bool = False
    socket_timeout: int = 30
    socket_connect_timeout: int = 30
    max_connections: int = 50
    
    @classmethod
    def from_env(cls) -> 'RedisConfig':
        return cls(
            host=os.getenv("REDIS_HOST", "localhost"),
            port=int(os.getenv("REDIS_PORT", "6379")),
            db=int(os.getenv("REDIS_DB", "0")),
            password=os.getenv("REDIS_PASSWORD"),
            decode_responses=os.getenv("REDIS_DECODE_RESPONSES", "false").lower() == "true",
            socket_timeout=int(os.getenv("REDIS_SOCKET_TIMEOUT", "30")),
            socket_connect_timeout=int(os.getenv("REDIS_SOCKET_CONNECT_TIMEOUT", "30")),
            max_connections=int(os.getenv("REDIS_MAX_CONNECTIONS", "50"))
        )

@dataclass
class EmbeddingConfig:
    """Embedding service configuration"""
    provider: str = "sentence_transformers"  # openai, ollama, sentence_transformers
    model_name: str = "all-MiniLM-L6-v2"
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    dimensions: Optional[int] = None
    max_retries: int = 3
    timeout: int = 30
    batch_size: int = 32
    
    @classmethod
    def from_env(cls) -> 'EmbeddingConfig':
        return cls(
            provider=os.getenv("EMBEDDING_PROVIDER", "sentence_transformers").lower(),
            model_name=os.getenv("EMBEDDING_MODEL", "all-MiniLM-L6-v2"),
            api_key=os.getenv("OPENAI_API_KEY") or os.getenv("EMBEDDING_API_KEY"),
            base_url=os.getenv("OLLAMA_BASE_URL", "http://localhost:11434"),
            dimensions=int(os.getenv("EMBEDDING_DIMENSIONS")) if os.getenv("EMBEDDING_DIMENSIONS") else None,
            max_retries=int(os.getenv("EMBEDDING_MAX_RETRIES", "3")),
            timeout=int(os.getenv("EMBEDDING_TIMEOUT", "30")),
            batch_size=int(os.getenv("EMBEDDING_BATCH_SIZE", "32"))
        )

@dataclass
class VectorStoreConfig:
    """Vector store configuration"""
    index_name: str = "vector_qa"
    doc_prefix: str = "doc:"
    similarity_threshold: float = 0.7
    max_results: int = 10
    enable_metadata_filtering: bool = True
    enable_caching: bool = True
    cache_ttl: int = 3600  # seconds
    
    @classmethod
    def from_env(cls) -> 'VectorStoreConfig':
        return cls(
            index_name=os.getenv("VECTOR_INDEX_NAME", "vector_qa"),
            doc_prefix=os.getenv("VECTOR_DOC_PREFIX", "doc:"),
            similarity_threshold=float(os.getenv("VECTOR_SIMILARITY_THRESHOLD", "0.7")),
            max_results=int(os.getenv("VECTOR_MAX_RESULTS", "10")),
            enable_metadata_filtering=os.getenv("VECTOR_ENABLE_METADATA_FILTERING", "true").lower() == "true",
            enable_caching=os.getenv("VECTOR_ENABLE_CACHING", "true").lower() == "true",
            cache_ttl=int(os.getenv("VECTOR_CACHE_TTL", "3600"))
        )

@dataclass
class APIConfig:
    """API server configuration"""
    host: str = "0.0.0.0"
    port: int = 8000
    reload: bool = False
    workers: int = 1
    log_level: str = "info"
    cors_origins: List[str] = field(default_factory=lambda: ["*"])
    cors_credentials: bool = False
    cors_methods: List[str] = field(default_factory=lambda: ["*"])
    cors_headers: List[str] = field(default_factory=lambda: ["*"])
    
    @classmethod
    def from_env(cls) -> 'APIConfig':
        cors_origins = os.getenv("CORS_ORIGINS", "*").split(",")
        cors_methods = os.getenv("CORS_METHODS", "*").split(",")
        cors_headers = os.getenv("CORS_HEADERS", "*").split(",")
        
        return cls(
            host=os.getenv("API_HOST", "0.0.0.0"),
            port=int(os.getenv("API_PORT", "8000")),
            reload=os.getenv("API_RELOAD", "false").lower() == "true",
            workers=int(os.getenv("API_WORKERS", "1")),
            log_level=os.getenv("API_LOG_LEVEL", "info"),
            cors_origins=cors_origins,
            cors_credentials=os.getenv("CORS_CREDENTIALS", "false").lower() == "true",
            cors_methods=cors_methods,
            cors_headers=cors_headers
        )

@dataclass
class LLMConfig:
    """LLM service configuration"""
    provider: str = "ollama"  # ollama, openai
    model_name: str = "qwen3:8b-q4_K_M"
    base_url: str = "http://localhost:11434"
    api_key: Optional[str] = None
    temperature: float = 0.5
    max_tokens: int = 150
    timeout: int = 120
    
    @classmethod
    def from_env(cls) -> 'LLMConfig':
        return cls(
            provider=os.getenv("LLM_PROVIDER", "ollama").lower(),
            model_name=os.getenv("LLM_MODEL", "qwen3:8b-q4_K_M"),
            base_url=os.getenv("LLM_BASE_URL", "http://localhost:11434"),
            api_key=os.getenv("LLM_API_KEY") or os.getenv("OPENAI_API_KEY"),
            temperature=float(os.getenv("LLM_TEMPERATURE", "0.5")),
            max_tokens=int(os.getenv("LLM_MAX_TOKENS", "150")),
            timeout=int(os.getenv("LLM_TIMEOUT", "120"))
        )

@dataclass
class AppConfig:
    """Main application configuration"""
    redis: RedisConfig = field(default_factory=RedisConfig)
    embedding: EmbeddingConfig = field(default_factory=EmbeddingConfig)
    vector_store: VectorStoreConfig = field(default_factory=VectorStoreConfig)
    api: APIConfig = field(default_factory=APIConfig)
    llm: LLMConfig = field(default_factory=LLMConfig)
    debug: bool = False
    environment: str = "development"
    
    @classmethod
    def from_env(cls) -> 'AppConfig':
        return cls(
            redis=RedisConfig.from_env(),
            embedding=EmbeddingConfig.from_env(),
            vector_store=VectorStoreConfig.from_env(),
            api=APIConfig.from_env(),
            llm=LLMConfig.from_env(),
            debug=os.getenv("DEBUG", "false").lower() == "true",
            environment=os.getenv("ENVIRONMENT", "development")
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        def convert_value(value):
            if hasattr(value, '__dict__'):
                return {k: convert_value(v) for k, v in value.__dict__.items()}
            elif isinstance(value, list):
                return [convert_value(item) for item in value]
            else:
                return value
        
        return convert_value(self)
    
    def save_to_file(self, filepath: str):
        """Save configuration to JSON file"""
        with open(filepath, 'w') as f:
            json.dump(self.to_dict(), f, indent=2)
    
    @classmethod
    def load_from_file(cls, filepath: str) -> 'AppConfig':
        """Load configuration from JSON file"""
        with open(filepath, 'r') as f:
            data = json.load(f)
        
        # This is a simplified loader - in practice, you'd want more robust deserialization
        config = cls()
        # Update config with loaded data (implementation would be more complex)
        return config

# Global configuration instance
_config: Optional[AppConfig] = None

def get_config() -> AppConfig:
    """Get the global configuration instance"""
    global _config
    if _config is None:
        _config = AppConfig.from_env()
    return _config

def set_config(config: AppConfig):
    """Set the global configuration instance"""
    global _config
    _config = config

def reload_config():
    """Reload configuration from environment variables"""
    global _config
    _config = AppConfig.from_env()

# Configuration validation
def validate_config(config: AppConfig) -> List[str]:
    """Validate configuration and return list of errors"""
    errors = []
    
    # Validate embedding configuration
    if config.embedding.provider == "openai" and not config.embedding.api_key:
        errors.append("OpenAI API key is required when using OpenAI embedding provider")
    
    if config.embedding.provider == "ollama" and not config.embedding.base_url:
        errors.append("Ollama base URL is required when using Ollama embedding provider")
    
    # Validate Redis configuration
    if not config.redis.host:
        errors.append("Redis host is required")
    
    if config.redis.port <= 0 or config.redis.port > 65535:
        errors.append("Redis port must be between 1 and 65535")
    
    # Validate vector store configuration
    if config.vector_store.similarity_threshold < 0 or config.vector_store.similarity_threshold > 1:
        errors.append("Similarity threshold must be between 0 and 1")
    
    if config.vector_store.max_results <= 0:
        errors.append("Max results must be greater than 0")
    
    # Validate API configuration
    if config.api.port <= 0 or config.api.port > 65535:
        errors.append("API port must be between 1 and 65535")
    
    return errors

def print_config_summary(config: Optional[AppConfig] = None):
    """Print a summary of the current configuration"""
    if config is None:
        config = get_config()
    
    print("=== Configuration Summary ===")
    print(f"Environment: {config.environment}")
    print(f"Debug: {config.debug}")
    print()
    print("Redis:")
    print(f"  Host: {config.redis.host}:{config.redis.port}")
    print(f"  Database: {config.redis.db}")
    print()
    print("Embedding:")
    print(f"  Provider: {config.embedding.provider}")
    print(f"  Model: {config.embedding.model_name}")
    print(f"  Dimensions: {config.embedding.dimensions or 'Auto-detect'}")
    print()
    print("Vector Store:")
    print(f"  Index: {config.vector_store.index_name}")
    print(f"  Similarity Threshold: {config.vector_store.similarity_threshold}")
    print(f"  Max Results: {config.vector_store.max_results}")
    print()
    print("API:")
    print(f"  Host: {config.api.host}:{config.api.port}")
    print(f"  Workers: {config.api.workers}")
    print(f"  CORS Origins: {config.api.cors_origins}")
    print()
    print("LLM:")
    print(f"  Provider: {config.llm.provider}")
    print(f"  Model: {config.llm.model_name}")
    print(f"  Base URL: {config.llm.base_url}")
    print("=" * 30)
