# Redis Vector Embeddings for Q&A Storage and Search

This implementation provides a comprehensive Redis-based vector embeddings system for storing user questions and AI answers with advanced similarity search capabilities.

## Features

- **Multiple Embedding Providers**: Support for OpenAI, Ollama, and Sentence Transformers
- **Advanced Vector Search**: Semantic similarity search with configurable thresholds
- **Metadata Filtering**: Filter search results by subject, difficulty, user, etc.
- **Caching System**: Intelligent caching for exact and similar question matches
- **RESTful API**: Complete FastAPI endpoints for all operations
- **Configuration Management**: Environment-based configuration system
- **Comprehensive Testing**: Unit and integration tests

## Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Configure Environment

Copy the example environment file and configure your settings:

```bash
cp .env.example .env
```

Edit `.env` to configure your preferred embedding provider:

```env
# For Sentence Transformers (default, no API key needed)
EMBEDDING_PROVIDER=sentence_transformers
EMBEDDING_MODEL=all-MiniLM-L6-v2

# For OpenAI (requires API key)
EMBEDDING_PROVIDER=openai
EMBEDDING_MODEL=text-embedding-3-small
OPENAI_API_KEY=your_api_key_here

# For Ollama (requires local Ollama server)
EMBEDDING_PROVIDER=ollama
EMBEDDING_MODEL=nomic-embed-text
OLLAMA_BASE_URL=http://localhost:11434
```

### 3. Start Redis

Make sure Redis is running:

```bash
redis-server
```

### 4. Run the API Server

```bash
python main.py
```

The API will be available at `http://localhost:8000` with interactive docs at `http://localhost:8000/docs`.

## API Endpoints

### Core Q&A Endpoints

#### Store Q&A Pair
```http
POST /vector/store/qa
Content-Type: application/json

{
  "question": "What is machine learning?",
  "answer": "Machine learning is a subset of AI...",
  "subject": "AI",
  "difficulty": "beginner",
  "metadata": {"source": "textbook"}
}
```

#### Search Similar Q&A Pairs
```http
POST /vector/search/qa
Content-Type: application/json

{
  "question": "Tell me about ML",
  "similarity_threshold": 0.75,
  "max_results": 5,
  "subject_filter": "AI"
}
```

#### Enhanced Ask (with vector search)
```http
POST /ask/enhanced
Content-Type: application/json

{
  "query": "What is deep learning?",
  "user_id": "user123",
  "lesson_id": "lesson_ai_basics"
}
```

### Vector Search Endpoints

#### General Vector Search
```http
POST /vector/search
Content-Type: application/json

{
  "query": "artificial intelligence",
  "similarity_threshold": 0.7,
  "max_results": 10,
  "filter_metadata": {"subject": "AI"}
}
```

#### Get Search Suggestions
```http
GET /vector/search/suggestions?query=machine%20learn&limit=5
```

#### Vector Store Statistics
```http
GET /vector/stats
```

### Health and Monitoring

#### Health Check
```http
GET /vector/health
```

#### Service Status
```http
GET /status
```

## Configuration Options

### Embedding Providers

#### Sentence Transformers (Local)
- **Pros**: No API key required, works offline, fast
- **Cons**: Limited model selection, requires local compute
- **Best for**: Development, privacy-sensitive applications

```env
EMBEDDING_PROVIDER=sentence_transformers
EMBEDDING_MODEL=all-MiniLM-L6-v2  # or all-mpnet-base-v2 for better quality
```

#### OpenAI (Cloud)
- **Pros**: High-quality embeddings, latest models
- **Cons**: Requires API key, costs money, internet dependency
- **Best for**: Production applications with budget

```env
EMBEDDING_PROVIDER=openai
EMBEDDING_MODEL=text-embedding-3-small  # or text-embedding-3-large
OPENAI_API_KEY=your_api_key_here
```

#### Ollama (Local)
- **Pros**: Local control, various models, no API costs
- **Cons**: Requires Ollama setup, more complex
- **Best for**: Local development with model flexibility

```env
EMBEDDING_PROVIDER=ollama
EMBEDDING_MODEL=nomic-embed-text  # or mxbai-embed-large
OLLAMA_BASE_URL=http://localhost:11434
```

### Search Configuration

```env
VECTOR_SIMILARITY_THRESHOLD=0.7  # Minimum similarity for results
VECTOR_MAX_RESULTS=10            # Maximum results per search
VECTOR_ENABLE_CACHING=true       # Enable result caching
VECTOR_CACHE_TTL=3600           # Cache TTL in seconds
```

## Usage Examples

### Python Client Example

```python
import requests

# Store a Q&A pair
response = requests.post("http://localhost:8000/vector/store/qa", json={
    "question": "What is neural network?",
    "answer": "A neural network is a computing system inspired by biological neural networks...",
    "subject": "AI",
    "difficulty": "intermediate"
})

# Search for similar questions
response = requests.post("http://localhost:8000/vector/search/qa", json={
    "question": "How do neural nets work?",
    "similarity_threshold": 0.7,
    "max_results": 3
})

similar_qa = response.json()
for qa in similar_qa["similar_qa_pairs"]:
    print(f"Q: {qa['question']}")
    print(f"A: {qa['answer']}")
    print(f"Similarity: {qa['similarity_score']:.3f}")
    print("---")
```

### JavaScript/Frontend Example

```javascript
// Search for similar Q&A pairs
async function searchSimilarQuestions(question) {
    const response = await fetch('/vector/search/qa', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            question: question,
            similarity_threshold: 0.75,
            max_results: 5
        })
    });
    
    const data = await response.json();
    return data.similar_qa_pairs;
}

// Enhanced ask with context
async function askWithContext(question, userId) {
    const response = await fetch('/ask/enhanced', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            query: question,
            user_id: userId
        })
    });
    
    const data = await response.json();
    return data.answer;
}
```

## Testing

Run the test suite:

```bash
# Unit tests only
python run_tests.py

# Include integration tests (requires Redis)
python run_tests.py --integration

# Run all tests
python run_tests.py --all
```

## Performance Considerations

### Embedding Generation
- **Sentence Transformers**: ~50-200ms per query (local)
- **OpenAI**: ~100-500ms per query (network dependent)
- **Ollama**: ~100-1000ms per query (model dependent)

### Search Performance
- **Small datasets** (<1K documents): <50ms
- **Medium datasets** (1K-10K documents): 50-200ms
- **Large datasets** (>10K documents): Consider Redis Search module

### Optimization Tips

1. **Use appropriate similarity thresholds**: Higher thresholds (0.8+) for exact matches, lower (0.6-0.7) for related content
2. **Enable caching**: Set `VECTOR_ENABLE_CACHING=true` for frequently searched queries
3. **Batch operations**: Store multiple Q&A pairs in batches when possible
4. **Monitor memory usage**: Large embedding vectors can consume significant Redis memory

## Troubleshooting

### Common Issues

1. **Redis Connection Failed**
   ```
   Solution: Ensure Redis server is running on the configured host/port
   ```

2. **Embedding Service Initialization Failed**
   ```
   Solution: Check API keys, model names, and network connectivity
   ```

3. **Low Search Quality**
   ```
   Solution: Try different embedding models or adjust similarity thresholds
   ```

4. **Slow Performance**
   ```
   Solution: Enable caching, use faster embedding models, or optimize Redis configuration
   ```

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   FastAPI App   │────│ Enhanced Vector  │────│ Redis Database  │
│                 │    │     Store        │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │
         │                       │
         ▼                       ▼
┌─────────────────┐    ┌──────────────────┐
│ Embedding       │    │ Search & Filter  │
│ Service         │    │ Engine           │
│ (OpenAI/Ollama/ │    │                  │
│ SentenceTransf) │    │                  │
└─────────────────┘    └──────────────────┘
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

This project is licensed under the MIT License.
