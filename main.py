from typing import List, Optional, Dict, Any
from fastapi import FastAP<PERSON>, Query, HTTPException
from fastapi.responses import PlainTextResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from redis import Redis
from datetime import datetime
import json
import numpy as np

# Import new enhanced services
from config import get_config, print_config_summary, validate_config
from embedding_service import get_embedding_service, EmbeddingServiceFactory
from enhanced_vector_store import get_vector_store, SearchConfig
from vector_search_api import router as vector_router

# Keep existing imports for backward compatibility
from redis_vector_store import create_index, store_question, search_similar, get_cached_response, add_to_history, model as embedding_model
from mistral_llm import get_answer
# Import managers with error handling
try:
    from lesson_manager import lesson_manager
    print("✅ Lesson manager imported successfully")
except Exception as e:
    print(f"⚠️ Error importing lesson_manager: {e}")
    lesson_manager = None

try:
    from user_learning_manager import user_learning_manager
    print("✅ User learning manager imported successfully")
except Exception as e:
    print(f"⚠️ Error importing user_learning_manager: {e}")
    user_learning_manager = None

app = FastAPI(
    title="Redis Vector Embeddings API",
    version="2.0.0",
    description="""
    Advanced Redis-based vector embeddings system for Q&A storage and semantic search.

    ## Features
    - **Multiple Embedding Providers**: OpenAI, Ollama, Sentence Transformers
    - **Semantic Search**: Find similar questions and answers using vector similarity
    - **Intelligent Caching**: Exact and approximate match caching
    - **Metadata Filtering**: Filter by subject, difficulty, user, etc.
    - **Enhanced Context**: Leverage similar Q&A pairs for better responses

    ## Quick Start
    1. Configure your embedding provider in environment variables
    2. Start Redis server
    3. Use `/ask/enhanced` for intelligent Q&A with context
    4. Use `/vector/search/qa` for finding similar questions

    ## Embedding Providers
    - **sentence_transformers** (default): Local, no API key needed
    - **openai**: High quality, requires API key
    - **ollama**: Local with model flexibility
    """,
    contact={
        "name": "Redis Vector Embeddings API",
        "url": "https://github.com/your-repo/redis-vector-embeddings",
    },
    license_info={
        "name": "MIT",
        "url": "https://opensource.org/licenses/MIT",
    },
)

# Get configuration
config = get_config()

# Enable CORS with configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=config.api.cors_origins,
    allow_credentials=config.api.cors_credentials,
    allow_methods=config.api.cors_methods,
    allow_headers=config.api.cors_headers
)

# Include vector search router
app.include_router(vector_router)

# Redis client (binary-safe; we handle encoding/decoding explicitly)
r = Redis(
    host=config.redis.host,
    port=config.redis.port,
    db=config.redis.db,
    password=config.redis.password,
    decode_responses=config.redis.decode_responses,
    socket_timeout=config.redis.socket_timeout,
    socket_connect_timeout=config.redis.socket_connect_timeout,
    max_connections=config.redis.max_connections
)

HISTORY_KEY = "history:{user_id}"
HISTORY_GLOBAL_KEY = "history:recent"
TS_KEY = "ts:scores:{user_id}"


class AskRequest(BaseModel):
    query: str = Field(..., description="User question")
    user_id: str = Field("default_user", description="User ID for personalization")
    lesson_id: str = Field(None, description="Optional lesson ID for context")


class AskResponse(BaseModel):
    query: str
    answer: str
    source: str  # "cache" | "llm"


class ProgressRequest(BaseModel):
    user_id: str
    score: float


class ProgressResponse(BaseModel):
    user_id: str
    stored: bool
    timestamp_ms: int


class RecommendationResponse(BaseModel):
    user_id: str
    recommendation: str  # "easy" | "hard"
    avg_score: Optional[float] = None
    count: int


class HistoryItem(BaseModel):
    timestamp_ms: int
    query: str
    answer: str
    source: str


class HistoryResponse(BaseModel):
    items: List[HistoryItem]


@app.on_event("startup")
def on_startup() -> None:
    print("🚀 Starting Redis Vector Embeddings API...")

    # Print configuration summary
    if config.debug:
        print_config_summary(config)

    # Validate configuration
    config_errors = validate_config(config)
    if config_errors:
        print("⚠️ Configuration errors found:")
        for error in config_errors:
            print(f"  - {error}")

    try:
        # Test Redis connection
        r.ping()
        print("✅ Redis connection successful")
    except Exception as e:
        print(f"❌ Redis connection failed: {e}")

    # Initialize embedding service
    try:
        embedding_service = get_embedding_service()
        provider_info = embedding_service.get_provider_info()
        print(f"✅ Embedding service initialized: {provider_info['provider']} ({provider_info['model_name']})")
    except Exception as e:
        print(f"❌ Embedding service initialization failed: {e}")

    # Initialize vector store
    try:
        vector_store = get_vector_store()
        stats = vector_store.get_stats()
        print(f"✅ Vector store initialized: {stats.get('total_documents', 0)} documents")
    except Exception as e:
        print(f"❌ Vector store initialization failed: {e}")

    # Ensure the legacy vector index exists for backward compatibility
    create_index()
    print("✅ Service startup complete")


@app.get("/",
         summary="API Information",
         description="Get API information and available endpoints")
def root() -> Dict[str, Any]:
    return {
        "status": "ok",
        "service": "Redis Vector Embeddings API",
        "version": "2.0.0",
        "docs": "/docs",
        "redoc": "/redoc",
        "features": [
            "Vector embeddings with multiple providers",
            "Semantic similarity search",
            "Q&A pair storage and retrieval",
            "Intelligent caching system",
            "Metadata filtering and search"
        ],
        "endpoints": {
            "core": [
                {"method": "POST", "path": "/ask", "description": "Legacy ask endpoint"},
                {"method": "POST", "path": "/ask/enhanced", "description": "Enhanced ask with vector search context"},
                {"method": "GET", "path": "/health", "description": "Health check"},
                {"method": "GET", "path": "/status", "description": "Service status"}
            ],
            "vector_search": [
                {"method": "POST", "path": "/vector/search", "description": "General vector similarity search"},
                {"method": "POST", "path": "/vector/search/qa", "description": "Search similar Q&A pairs"},
                {"method": "POST", "path": "/vector/store/qa", "description": "Store new Q&A pair"},
                {"method": "GET", "path": "/vector/stats", "description": "Vector store statistics"},
                {"method": "GET", "path": "/vector/health", "description": "Vector service health"}
            ],
            "user_management": [
                {"method": "GET", "path": "/user/{user_id}/history", "description": "User learning history"},
                {"method": "GET", "path": "/user/{user_id}/progress", "description": "User progress summary"},
                {"method": "GET", "path": "/user/{user_id}/recommendations", "description": "Personalized recommendations"}
            ]
        }
    }


def generate_embedding(text: str) -> np.ndarray:
    """Generate an embedding vector for input text."""
    return embedding_model.encode(text).astype(np.float32)


# Util: record interaction history

def record_history_global(query: str, answer: str, source: str) -> None:
    key = HISTORY_GLOBAL_KEY
    payload = {
        "timestamp_ms": int(datetime.utcnow().timestamp() * 1000),
        "query": query,
        "answer": answer,
        "source": source,
    }
    # LPUSH newest-first; keep last 100 entries
    r.lpush(key, json.dumps(payload).encode("utf-8"))
    r.ltrim(key, 0, 99)


@app.get("/health", response_class=PlainTextResponse)
def health() -> str:
    try:
        # Test Redis connection
        r.ping()
        return "OK"
    except Exception as e:
        print(f"Redis connection error: {e}")
        return "Redis connection failed"

@app.get("/test")
def test() -> Dict[str, Any]:
    return {"message": "API is working", "timestamp": datetime.now().isoformat()}

@app.get("/status")
def status() -> Dict[str, Any]:
    """Check the status of various services"""
    try:
        # Test Redis
        redis_status = "connected" if r.ping() else "disconnected"
    except:
        redis_status = "error"
    
    try:
        # Test Ollama
        import requests
        ollama_response = requests.get("http://localhost:11434/api/tags", timeout=5)
        ollama_status = "connected" if ollama_response.status_code == 200 else "error"
    except:
        ollama_status = "disconnected"
    
    return {
        "api": "running",
        "redis": redis_status,
        "ollama": ollama_status,
        "timestamp": datetime.now().isoformat()
    }

@app.get("/debug/redis")
def debug_redis() -> Dict[str, Any]:
    """Debug endpoint to check what's stored in Redis"""
    try:
        # Check new history list
        history_items = r.lrange("history", 0, 9)  # Last 10 items
        history_data = []
        for item in history_items:
            try:
                data = json.loads(item.decode('utf-8'))
                history_data.append({
                    "query": data.get("query", "")[:50] + "...",
                    "response": data.get("response", "")[:100] + "...",
                    "timestamp": data.get("timestamp_ms", 0)
                })
            except Exception as e:
                print(f"Error parsing history item in debug: {e}")
                continue
        
        # Check query cache
        query_keys = r.keys("query:*")
        query_cache_count = len(query_keys)
        
        # Check vector questions
        question_keys = r.keys(f"question:*")
        questions_count = len(question_keys)
        
        return {
            "redis_connected": True,
            "history_count": len(history_items),
            "query_cache_count": query_cache_count,
            "vector_questions_count": questions_count,
            "recent_history": history_data,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        return {
            "redis_connected": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }


# Remove the manual OPTIONS handler since CORS middleware should handle it


@app.post("/ask", response_model=AskResponse)
def ask(req: AskRequest) -> AskResponse:
    try:
        print(f"Processing query: {req.query} for user: {req.user_id}")
        
        # 1) First check exact cache match
        print("Checking exact cache...")
        cached_response = get_cached_response(req.query)
        if cached_response and cached_response.strip():  # Check if response exists and is not empty
            print("✅ Exact cache hit")
            # Record user interaction
            if user_learning_manager:
                try:
                    user_learning_manager.record_user_question(
                        req.user_id, req.query, cached_response,
                        lesson_id=req.lesson_id, difficulty="beginner", score=1.0
                    )
                except Exception as e:
                    print(f"⚠️ Warning: Could not record user question: {e}")
            record_history_global(req.query, cached_response, source="cache")
            return AskResponse(query=req.query, answer=cached_response, source="cache")

        # 2) Check if we have a quick response for common questions
        # Quick responses removed - all questions will go through the LLM for dynamic responses

        # 3) Check lesson-specific content for personalized context
        lesson_context = ""
        if req.lesson_id and lesson_manager:
            lesson = lesson_manager.get_lesson(req.lesson_id)
            if lesson:
                lesson_context = f"Context from lesson '{lesson['title']}': {lesson['content'][:500]}...\n\n"
                print(f"✅ Found lesson context: {lesson['title']}")

        # 4) Check vector similarity search
        print("Checking vector similarity...")
        similar = search_similar(req.query, top_k=1)
        if similar:
            top_score, cached_answer = similar[0]
            if top_score > 0.90:
                print(f"Vector cache hit with score {top_score}")
                if user_learning_manager:
                    try:
                        user_learning_manager.record_user_question(
                            req.user_id, req.query, cached_answer,
                            lesson_id=req.lesson_id, difficulty="beginner", score=top_score
                        )
                    except Exception as e:
                        print(f"⚠️ Warning: Could not record user question: {e}")
                record_history_global(req.query, cached_answer, source="vector_cache")
                return AskResponse(query=req.query, answer=cached_answer, source="vector_cache")

        # 5) Search lesson content for relevant information
        print("Searching lesson content...")
        relevant_lessons = []
        if lesson_manager:
            relevant_lessons = lesson_manager.search_lessons(req.query, limit=2)
        lesson_context = ""
        if relevant_lessons:
            lesson_context = "Relevant lesson information:\n"
            for lesson in relevant_lessons:
                lesson_context += f"- {lesson['title']}: {lesson['content'][:300]}...\n"
            lesson_context += "\n"
            print(f"✅ Found {len(relevant_lessons)} relevant lessons")

        # 6) Otherwise call LLM with personalized context
        print("Calling LLM with personalized context...")
        enhanced_query = f"{lesson_context}Please answer the following question directly and clearly without using any special formatting or tags: {req.query}"
        llm_answer = get_answer(enhanced_query)
        
        # Clean up the response by removing <think> tags and extra formatting
        if "<think>" in llm_answer:
            # Remove everything between <think> and </think> tags
            import re
            llm_answer = re.sub(r'<think>.*?</think>', '', llm_answer, flags=re.DOTALL).strip()
        
        # Check if LLM returned an error
        if llm_answer.startswith("Error:"):
            # Provide a helpful error message
            llm_answer = f"I'm sorry, I encountered an issue with the AI service: {llm_answer}. Please try again in a moment."
        
        print("Generating embedding...")
        query_embedding: np.ndarray = generate_embedding(req.query)
        
        # Use a stable key per query text (use sha1 for stability across runs)
        import hashlib
        qid = f"q:{hashlib.sha1(req.query.encode('utf-8')).hexdigest()}"
        
        print("Storing in Redis...")
        store_question(qid, req.query, query_embedding, llm_answer)
        
        # Record user interaction with estimated score
        estimated_score = 0.7  # Default score for new questions
        if user_learning_manager:
            try:
                user_learning_manager.record_user_question(
                    req.user_id, req.query, llm_answer, 
                    lesson_id=req.lesson_id, difficulty="beginner", score=estimated_score
                )
            except Exception as e:
                print(f"⚠️ Warning: Could not record user question: {e}")
        
        record_history_global(req.query, llm_answer, source="llm")
        
        print("Request completed successfully")
        return AskResponse(query=req.query, answer=llm_answer, source="llm")
    except Exception as e:
        print(f"Error in /ask endpoint: {str(e)}")
        # Return a more user-friendly error response
        return AskResponse(
            query=req.query,
            answer=f"I'm sorry, I encountered an error while processing your request. Please try again. Error: {str(e)}",
            source="error"
        )


@app.post("/ask/enhanced", response_model=AskResponse)
def ask_enhanced(req: AskRequest) -> AskResponse:
    """Enhanced ask endpoint using the new vector search system"""
    try:
        print(f"Processing enhanced query: {req.query} for user: {req.user_id}")

        # Get vector store and embedding service
        vector_store = get_vector_store()

        # 1) First check for exact matches in the enhanced vector store
        search_config = SearchConfig(
            similarity_threshold=0.95,  # Very high threshold for exact matches
            max_results=1,
            filter_metadata={"type": "qa_pair"}
        )

        exact_matches = vector_store.search_qa_pairs(req.query, search_config)
        if exact_matches and exact_matches[0]["similarity_score"] > 0.95:
            cached_answer = exact_matches[0]["answer"]
            print("✅ Enhanced exact match found")

            # Record user interaction
            if user_learning_manager:
                try:
                    user_learning_manager.record_user_question(
                        req.user_id, req.query, cached_answer,
                        lesson_id=req.lesson_id, difficulty="beginner", score=1.0
                    )
                except Exception as e:
                    print(f"⚠️ Warning: Could not record user question: {e}")

            record_history_global(req.query, cached_answer, source="enhanced_cache")
            return AskResponse(query=req.query, answer=cached_answer, source="enhanced_cache")

        # 2) Check for similar questions with lower threshold
        search_config.similarity_threshold = 0.8
        search_config.max_results = 3

        similar_matches = vector_store.search_qa_pairs(req.query, search_config)

        # 3) Build context from similar questions
        context = ""
        if similar_matches:
            context = "Similar questions and answers for context:\n"
            for i, match in enumerate(similar_matches[:2], 1):
                context += f"{i}. Q: {match['question']}\n   A: {match['answer'][:200]}...\n"
            context += "\nBased on the above context, please answer the following question:\n"
            print(f"✅ Found {len(similar_matches)} similar questions for context")

        # 4) Get lesson context if available
        lesson_context = ""
        if req.lesson_id and lesson_manager:
            lesson = lesson_manager.get_lesson(req.lesson_id)
            if lesson:
                lesson_context = f"Lesson context from '{lesson['title']}': {lesson['content'][:500]}...\n\n"
                print(f"✅ Found lesson context: {lesson['title']}")

        # 5) Call LLM with enhanced context
        enhanced_query = f"{lesson_context}{context}Question: {req.query}\n\nPlease provide a clear, direct answer:"
        llm_answer = get_answer(enhanced_query)

        # Clean up the response
        if "<think>" in llm_answer:
            import re
            llm_answer = re.sub(r'<think>.*?</think>', '', llm_answer, flags=re.DOTALL).strip()

        if llm_answer.startswith("Error:"):
            llm_answer = f"I'm sorry, I encountered an issue with the AI service: {llm_answer}. Please try again in a moment."

        # 6) Store the new Q&A pair in the enhanced vector store
        metadata = {
            "user_id": req.user_id,
            "lesson_id": req.lesson_id,
            "subject": "general",
            "difficulty": "beginner",
            "has_context": bool(context or lesson_context),
            "similar_questions_count": len(similar_matches)
        }

        try:
            doc_id = vector_store.store_qa_pair(req.query, llm_answer, metadata)
            print(f"✅ Stored enhanced Q&A pair: {doc_id}")
        except Exception as e:
            print(f"⚠️ Warning: Could not store Q&A pair in enhanced store: {e}")

        # 7) Also store in legacy system for backward compatibility
        try:
            embedding_service = get_embedding_service()
            query_embedding = embedding_service.get_embedding(req.query)

            import hashlib
            qid = f"q:{hashlib.sha1(req.query.encode('utf-8')).hexdigest()}"
            store_question(qid, req.query, query_embedding, llm_answer)
        except Exception as e:
            print(f"⚠️ Warning: Could not store in legacy system: {e}")

        # Record user interaction
        estimated_score = 0.8 if similar_matches else 0.7
        if user_learning_manager:
            try:
                user_learning_manager.record_user_question(
                    req.user_id, req.query, llm_answer,
                    lesson_id=req.lesson_id, difficulty="beginner", score=estimated_score
                )
            except Exception as e:
                print(f"⚠️ Warning: Could not record user question: {e}")

        record_history_global(req.query, llm_answer, source="enhanced_llm")

        print("Enhanced request completed successfully")
        return AskResponse(query=req.query, answer=llm_answer, source="enhanced_llm")

    except Exception as e:
        print(f"Error in /ask/enhanced endpoint: {str(e)}")
        # Fallback to regular ask endpoint
        try:
            return ask(req)
        except:
            return AskResponse(
                query=req.query,
                answer=f"I'm sorry, I encountered an error while processing your request. Please try again. Error: {str(e)}",
                source="error"
            )


@app.post("/progress", response_model=ProgressResponse)
def progress(req: ProgressRequest) -> ProgressResponse:
    key = TS_KEY.format(user_id=req.user_id)
    ts = r.ts()
    timestamp_ms = int(datetime.utcnow().timestamp() * 1000)

    # Create series if not exists; TS.ADD will create by default in newer RedisTimeSeries
    try:
        ts.add(key, timestamp_ms, float(req.score))
    except Exception as e:
        # Try to create then add
        try:
            ts.create(key, labels={"user_id": req.user_id})
        except Exception:
            # ignore if it already exists now
            pass
        ts.add(key, timestamp_ms, float(req.score))

    return ProgressResponse(user_id=req.user_id, stored=True, timestamp_ms=timestamp_ms)


@app.get("/recommendations", response_model=RecommendationResponse)
def recommendations(user_id: str = Query(...)) -> RecommendationResponse:
    key = TS_KEY.format(user_id=user_id)
    ts = r.ts()

    # Fetch last 5 scores
    try:
        # Prefer REVRANGE if available
        values = ts.revrange(key, from_time="-", to_time="+", count=5)
    except Exception:
        # Fallback: RANGE and take last 5
        all_values = ts.range(key, from_time="-", to_time="+")
        values = list(reversed(all_values[-5:])) if all_values else []

    count = len(values)
    if count == 0:
        return RecommendationResponse(user_id=user_id, recommendation="easy", avg_score=None, count=0)

    scores = [float(v) for (_ts, v) in values]
    avg_score = sum(scores) / len(scores)

    # Simple heuristic: higher recent scores => recommend harder
    recommendation = "hard" if avg_score >= 0.7 else "easy"

    return RecommendationResponse(user_id=user_id, recommendation=recommendation, avg_score=avg_score, count=count)


@app.get("/history")
def history(limit: int = Query(10, ge=1, le=100)) -> HistoryResponse:
    try:
        # Use the new "history" list instead of HISTORY_GLOBAL_KEY
        raw_items = r.lrange("history", 0, limit - 1)
        items: List[HistoryItem] = []
        for raw in raw_items:
            try:
                obj: Dict[str, Any] = json.loads(raw.decode("utf-8"))
                # Convert the new format to HistoryItem format
                history_item = HistoryItem(
                    timestamp_ms=obj.get("timestamp_ms", 0),
                    query=obj.get("query", ""),
                    answer=obj.get("response", ""),  # Note: new format uses "response"
                    source="cache"  # Default source for history items
                )
                items.append(history_item)
            except Exception as e:
                print(f"Error parsing history item: {e}")
                continue

        return HistoryResponse(items=items)
    except Exception as e:
        print(f"Error in /history endpoint: {str(e)}")
        # Return empty history instead of throwing error
        return HistoryResponse(items=[])

# New Learning Endpoints
@app.get("/user/{user_id}/history")
def get_user_history(user_id: str, limit: int = Query(20, ge=1, le=100)) -> Dict[str, Any]:
    """Get personalized user learning history"""
    try:
        if user_learning_manager is None:
            return {"user_id": user_id, "history": [], "count": 0, "error": "User learning manager not available"}
        
        history = user_learning_manager.get_user_history(user_id, limit=limit)
        return {
            "user_id": user_id,
            "history": history,
            "count": len(history)
        }
    except Exception as e:
        print(f"Error getting user history: {e}")
        return {"user_id": user_id, "history": [], "count": 0}

@app.get("/user/{user_id}/progress")
def get_user_progress(user_id: str) -> Dict[str, Any]:
    """Get user's learning progress summary"""
    try:
        if user_learning_manager is None:
            return {
                "user_id": user_id,
                "total_lessons": 0,
                "completed_lessons": 0,
                "completion_rate": 0,
                "average_score": 0,
                "total_time_spent": 0,
                "error": "User learning manager not available"
            }
        
        progress = user_learning_manager.get_user_progress(user_id)
        return progress
    except Exception as e:
        print(f"Error getting user progress: {e}")
        return {"user_id": user_id, "error": str(e)}

@app.get("/user/{user_id}/analytics")
def get_user_analytics(user_id: str, days: int = Query(30, ge=1, le=365)) -> Dict[str, Any]:
    """Get user analytics for the last N days"""
    try:
        if user_learning_manager is None:
            return {
                "user_id": user_id,
                "daily_activity": [],
                "total_questions": 0,
                "total_lessons_completed": 0,
                "average_score": 0,
                "total_time_spent": 0,
                "difficulty_distribution": {},
                "error": "User learning manager not available"
            }
        
        analytics = user_learning_manager.get_user_analytics(user_id, days=days)
        return analytics
    except Exception as e:
        print(f"Error getting user analytics: {e}")
        return {"user_id": user_id, "error": str(e)}

@app.get("/user/{user_id}/recommendations")
def get_user_recommendations(user_id: str, subject: str = Query(None), 
                           limit: int = Query(5, ge=1, le=20)) -> Dict[str, Any]:
    """Get personalized lesson recommendations for user"""
    try:
        if user_learning_manager is None:
            return {"user_id": user_id, "recommendations": [], "count": 0, "error": "User learning manager not available"}
        
        recommendations = user_learning_manager.get_recommended_lessons(
            user_id, subject=subject, limit=limit
        )
        return {
            "user_id": user_id,
            "recommendations": recommendations,
            "count": len(recommendations)
        }
    except Exception as e:
        print(f"Error getting recommendations: {e}")
        return {"user_id": user_id, "recommendations": [], "count": 0}

@app.get("/user/{user_id}/recent-activity")
def get_recent_activity(user_id: str, limit: int = Query(10, ge=1, le=50)) -> Dict[str, Any]:
    """Get user's recent activity"""
    try:
        if user_learning_manager is None:
            return {"user_id": user_id, "activities": [], "count": 0, "error": "User learning manager not available"}
        
        activities = user_learning_manager.get_recent_activity(user_id, limit=limit)
        return {
            "user_id": user_id,
            "activities": activities,
            "count": len(activities)
        }
    except Exception as e:
        print(f"Error getting recent activity: {e}")
        return {"user_id": user_id, "activities": [], "count": 0}

@app.get("/user/{user_id}/learning-history")
def get_learning_history(user_id: str, limit: int = Query(20, ge=1, le=100)) -> Dict[str, Any]:
    """Get user's learning history timeline"""
    try:
        if user_learning_manager is None:
            return {"user_id": user_id, "history": [], "count": 0, "error": "User learning manager not available"}
        
        history = user_learning_manager.get_learning_history(user_id, limit=limit)
        return {
            "user_id": user_id,
            "history": history,
            "count": len(history)
        }
    except Exception as e:
        print(f"Error getting learning history: {e}")
        return {"user_id": user_id, "history": [], "count": 0}

@app.get("/user/{user_id}/dashboard")
def get_dashboard_data(user_id: str) -> Dict[str, Any]:
    """Get all dashboard data in one call"""
    try:
        if user_learning_manager is None:
            return {
                "user_id": user_id,
                "recent_activity": [],
                "learning_history": [],
                "recommendations": [],
                "progress": {
                    "user_id": user_id,
                    "total_lessons": 0,
                    "completed_lessons": 0,
                    "completion_rate": 0,
                    "average_score": 0,
                    "total_time_spent": 0
                },
                "analytics": {
                    "daily_activity": [],
                    "total_questions": 0,
                    "total_lessons_completed": 0,
                    "average_score": 0,
                    "total_time_spent": 0,
                    "difficulty_distribution": {}
                },
                "error": "User learning manager not available"
            }
        
        dashboard_data = user_learning_manager.get_dashboard_data(user_id)
        return dashboard_data
    except Exception as e:
        print(f"Error getting dashboard data: {e}")
        return {
            "user_id": user_id,
            "recent_activity": [],
            "learning_history": [],
            "recommendations": [],
            "progress": {
                "user_id": user_id,
                "total_lessons": 0,
                "completed_lessons": 0,
                "completion_rate": 0,
                "average_score": 0,
                "total_time_spent": 0
            },
            "analytics": {
                "daily_activity": [],
                "total_questions": 0,
                "total_lessons_completed": 0,
                "average_score": 0,
                "total_time_spent": 0,
                "difficulty_distribution": {}
            },
            "error": str(e)
        }

@app.post("/user/{user_id}/complete-lesson")
def complete_lesson(user_id: str, lesson_id: str, completion_score: float = Query(..., ge=0, le=1),
                   time_spent: int = Query(0, ge=0)) -> Dict[str, Any]:
    """Record lesson completion for user"""
    try:
        if user_learning_manager is None:
            return {"user_id": user_id, "lesson_id": lesson_id, "success": False, "error": "User learning manager not available"}
        
        success = user_learning_manager.record_lesson_completion(
            user_id, lesson_id, completion_score, time_spent
        )
        return {
            "user_id": user_id,
            "lesson_id": lesson_id,
            "success": success,
            "completion_score": completion_score,
            "time_spent": time_spent
        }
    except Exception as e:
        print(f"Error recording lesson completion: {e}")
        return {"user_id": user_id, "lesson_id": lesson_id, "success": False, "error": str(e)}

# Lesson Management Endpoints
@app.post("/lessons")
def create_lesson(title: str = Query(...), content: str = Query(...), 
                 difficulty: str = Query("beginner"), subject: str = Query("general"),
                 tags: str = Query("")) -> Dict[str, Any]:
    """Create a new lesson"""
    try:
        tag_list = [tag.strip() for tag in tags.split(",") if tag.strip()] if tags else []
        lesson_id = lesson_manager.create_lesson(title, content, difficulty, subject, tag_list)
        
        if lesson_id:
            return {
                "success": True,
                "lesson_id": lesson_id,
                "title": title,
                "difficulty": difficulty,
                "subject": subject
            }
        else:
            return {"success": False, "error": "Failed to create lesson"}
            
    except Exception as e:
        print(f"Error creating lesson: {e}")
        return {"success": False, "error": str(e)}

@app.get("/lessons")
def get_lessons(difficulty: str = Query(None), subject: str = Query(None),
                limit: int = Query(10, ge=1, le=50)) -> Dict[str, Any]:
    """Get lessons with optional filtering"""
    try:
        if difficulty:
            lessons = lesson_manager.get_lessons_by_difficulty(difficulty, subject, limit)
        else:
            # Get all lessons (simplified for now)
            lesson_keys = r.keys("lesson:*")
            lessons = []
            for key in lesson_keys[:limit]:
                try:
                    lesson_data = r.get(key)
                    if lesson_data:
                        lesson = json.loads(lesson_data.decode('utf-8'))
                        if not subject or lesson.get('subject') == subject:
                            lessons.append(lesson)
                except Exception:
                    continue
        
        return {
            "lessons": lessons,
            "count": len(lessons),
            "filters": {"difficulty": difficulty, "subject": subject}
        }
        
    except Exception as e:
        print(f"Error getting lessons: {e}")
        return {"lessons": [], "count": 0, "error": str(e)}

@app.get("/lessons/search")
def search_lessons(query: str = Query(...), subject: str = Query(None),
                  difficulty: str = Query(None), limit: int = Query(5, ge=1, le=20)) -> Dict[str, Any]:
    """Search lessons by semantic similarity"""
    try:
        results = lesson_manager.search_lessons(query, subject, difficulty, limit)
        return {
            "query": query,
            "results": results,
            "count": len(results)
        }
    except Exception as e:
        print(f"Error searching lessons: {e}")
        return {"query": query, "results": [], "count": 0, "error": str(e)}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=False)
