#!/usr/bin/env python3
"""
Test runner for Redis Vector Embeddings system
"""

import sys
import subprocess
import os
from pathlib import Path

def run_tests():
    """Run all tests with pytest"""
    print("🧪 Running Redis Vector Embeddings Tests...")
    print("=" * 50)
    
    # Ensure we're in the right directory
    os.chdir(Path(__file__).parent)
    
    # Install test dependencies if needed
    try:
        import pytest
    except ImportError:
        print("Installing pytest...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pytest"])
    
    # Run tests
    test_files = [
        "test_vector_embeddings.py"
    ]
    
    for test_file in test_files:
        if os.path.exists(test_file):
            print(f"\n📋 Running {test_file}...")
            result = subprocess.run([
                sys.executable, "-m", "pytest", 
                test_file, 
                "-v", 
                "--tb=short"
            ])
            
            if result.returncode != 0:
                print(f"❌ Tests failed in {test_file}")
                return False
            else:
                print(f"✅ Tests passed in {test_file}")
        else:
            print(f"⚠️ Test file {test_file} not found")
    
    print("\n🎉 All tests completed!")
    return True

def run_integration_tests():
    """Run integration tests that require Redis"""
    print("\n🔧 Running Integration Tests...")
    print("Note: These tests require a running Redis instance")
    
    # Check if Redis is available
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        print("✅ Redis connection successful")
    except Exception as e:
        print(f"❌ Redis not available: {e}")
        print("Please start Redis server before running integration tests")
        return False
    
    # Run integration tests
    result = subprocess.run([
        sys.executable, "-m", "pytest", 
        "test_vector_embeddings.py::TestIntegration",
        "-v"
    ])
    
    return result.returncode == 0

def main():
    """Main test runner"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Run Redis Vector Embeddings tests")
    parser.add_argument("--integration", action="store_true", 
                       help="Run integration tests (requires Redis)")
    parser.add_argument("--all", action="store_true", 
                       help="Run all tests including integration")
    
    args = parser.parse_args()
    
    success = True
    
    # Run unit tests
    if not run_tests():
        success = False
    
    # Run integration tests if requested
    if args.integration or args.all:
        if not run_integration_tests():
            success = False
    
    if success:
        print("\n🎉 All tests passed!")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
