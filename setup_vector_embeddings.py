#!/usr/bin/env python3
"""
Setup script for Redis Vector Embeddings system
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    return True

def check_redis():
    """Check if Redis is available"""
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        print("✅ Redis is running and accessible")
        return True
    except ImportError:
        print("⚠️ Redis Python client not installed (will be installed with requirements)")
        return True
    except Exception as e:
        print(f"❌ Redis is not running or not accessible: {e}")
        print("Please start Redis server before running the application")
        return False

def install_requirements():
    """Install Python requirements"""
    print("📦 Installing Python requirements...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ Requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install requirements: {e}")
        return False

def setup_environment():
    """Setup environment configuration"""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if env_file.exists():
        print("✅ .env file already exists")
        return True
    
    if env_example.exists():
        print("📝 Creating .env file from template...")
        shutil.copy(env_example, env_file)
        print("✅ .env file created")
        print("📋 Please edit .env file to configure your embedding provider")
        return True
    else:
        print("⚠️ .env.example not found, creating basic .env file...")
        
        basic_env = """# Redis Vector Embeddings Configuration
ENVIRONMENT=development
DEBUG=false

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# Embedding Service (default: sentence_transformers)
EMBEDDING_PROVIDER=sentence_transformers
EMBEDDING_MODEL=all-MiniLM-L6-v2

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000

# Vector Store Configuration
VECTOR_SIMILARITY_THRESHOLD=0.7
VECTOR_MAX_RESULTS=10
"""
        
        with open(env_file, 'w') as f:
            f.write(basic_env)
        
        print("✅ Basic .env file created")
        return True

def test_embedding_providers():
    """Test available embedding providers"""
    print("\n🧪 Testing embedding providers...")
    
    # Test Sentence Transformers
    try:
        from sentence_transformers import SentenceTransformer
        model = SentenceTransformer('all-MiniLM-L6-v2')
        test_embedding = model.encode("test")
        print(f"✅ Sentence Transformers: Working (dimensions: {len(test_embedding)})")
    except Exception as e:
        print(f"❌ Sentence Transformers: {e}")
    
    # Test OpenAI (if API key is available)
    openai_key = os.getenv("OPENAI_API_KEY")
    if openai_key:
        try:
            import openai
            client = openai.OpenAI(api_key=openai_key)
            response = client.embeddings.create(
                model="text-embedding-3-small",
                input="test"
            )
            print(f"✅ OpenAI: Working (dimensions: {len(response.data[0].embedding)})")
        except Exception as e:
            print(f"❌ OpenAI: {e}")
    else:
        print("⚠️ OpenAI: No API key found (set OPENAI_API_KEY to test)")
    
    # Test Ollama
    try:
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            print("✅ Ollama: Server is running")
        else:
            print("❌ Ollama: Server responded with error")
    except Exception as e:
        print(f"⚠️ Ollama: Not available ({e})")

def run_basic_test():
    """Run a basic functionality test"""
    print("\n🔧 Running basic functionality test...")
    
    try:
        # Test configuration loading
        from config import get_config
        config = get_config()
        print("✅ Configuration loading works")
        
        # Test embedding service
        from embedding_service import get_embedding_service
        embedding_service = get_embedding_service()
        test_embedding = embedding_service.get_embedding("test")
        print(f"✅ Embedding service works (dimensions: {len(test_embedding)})")
        
        # Test vector store (without Redis for now)
        print("✅ Core modules import successfully")
        
        return True
    except Exception as e:
        print(f"❌ Basic test failed: {e}")
        return False

def print_next_steps():
    """Print next steps for the user"""
    print("\n🎯 Next Steps:")
    print("1. Start Redis server if not already running:")
    print("   redis-server")
    print()
    print("2. Configure your embedding provider in .env file:")
    print("   - sentence_transformers (default, no setup needed)")
    print("   - openai (requires OPENAI_API_KEY)")
    print("   - ollama (requires Ollama server)")
    print()
    print("3. Start the API server:")
    print("   python main.py")
    print()
    print("4. Run the demo:")
    print("   python demo_vector_search.py")
    print()
    print("5. Visit the API documentation:")
    print("   http://localhost:8000/docs")
    print()
    print("6. Run tests:")
    print("   python run_tests.py")

def main():
    """Main setup function"""
    print("🚀 Redis Vector Embeddings Setup")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install requirements
    if not install_requirements():
        print("❌ Setup failed during requirements installation")
        sys.exit(1)
    
    # Setup environment
    if not setup_environment():
        print("❌ Setup failed during environment configuration")
        sys.exit(1)
    
    # Check Redis
    redis_ok = check_redis()
    
    # Test embedding providers
    test_embedding_providers()
    
    # Run basic test
    if not run_basic_test():
        print("❌ Setup completed but basic test failed")
        print("Please check the error messages above")
    
    print("\n✅ Setup completed successfully!")
    
    if not redis_ok:
        print("\n⚠️ Note: Redis is not running. Please start Redis before using the application.")
    
    print_next_steps()

if __name__ == "__main__":
    main()
